import { FC, useRef, useState } from "react";
import { UseFormSetValue, UseFormTrigger } from "react-hook-form";
import styles from "./FileUpload.module.scss";

interface FileUploadProps {
  label: string;
  error?: string;
  setValue: UseFormSetValue<any>;
  trigger?: UseFormTrigger<any>;
  required?: boolean;
  accept?: string;
  fieldName: string;
}

const FileUpload: FC<FileUploadProps> = ({
  label,
  error,
  setValue,
  trigger,
  required,
  accept = "image/*,.pdf",
  fieldName,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setValue(fieldName, file);
      if (trigger) {
        await trigger(fieldName);
      }
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = async (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      setSelectedFile(file);
      setValue(fieldName, file);
      if (trigger) {
        await trigger(fieldName);
      }
    }
  };

  return (
    <div className={styles.fileUploadWrapper}>
      <label className={styles.label}>
        {label}
        {required && <span className={styles.required}>*</span>}
      </label>

      <div
        className={`${styles.dropZone} ${error ? styles.error : ""}`}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          accept={accept}
          onChange={handleFileSelect}
          className={styles.hiddenInput}
        />

        {selectedFile ? (
          <div className={styles.fileSelected}>
            <span className={styles.fileName}>{selectedFile.name}</span>
            <span className={styles.fileSize}>
              ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </span>
          </div>
        ) : (
          <div className={styles.dropContent}>
            <span className={styles.uploadIcon}>📁</span>
            <span className={styles.uploadText}>
              Click to upload or drag and drop
            </span>
            <span className={styles.uploadSubtext}>
              Supported formats: Images, PDF (Max 10MB)
            </span>
          </div>
        )}
      </div>

      {error && <span className={styles.errorMessage}>{error}</span>}
    </div>
  );
};

export default FileUpload;
