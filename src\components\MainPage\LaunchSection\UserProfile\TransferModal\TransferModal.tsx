import { FC, useState } from "react";
import styles from "./TransferModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { MinePurchased } from "../../PurchaseHistory/hooks/usePurchaseHistory";
import KYCModal from "./KYCModal/KYCModal";
import { useAccount, useNetwork } from "wagmi";
import useTransferNfts from "../hooks/useTransferNfts";

interface TransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  eligibleMines: MinePurchased[];
}

interface KYCFormData {
  name: string;
  email: string;
  transferNftNumber: number;
  idType: string;
  idFile: File;
}

const TransferModal: FC<TransferModalProps> = ({
  isOpen,
  onClose,
  eligibleMines,
}) => {
  const [isKYCModalOpen, setIsKYCModalOpen] = useState(false);
  const [selectedMine, setSelectedMine] = useState<MinePurchased | null>(null);
  const { address } = useAccount();
  const { chain } = useNetwork();
  const operationProps = {
    setIsKYCModalOpen,
    setSelectedMine,
    onClose,
  };

  const {
    setTransferNftAmount,
    handleTransferNft,
    isApproving,
    isWaitingFortransferNft,
    setUploadData,
    erc1155VaultAddress,
  } = useTransferNfts({ ...operationProps });

  const transferNftsProps = {
    setTransferNftAmount,
    handleTransferNft,
    isApproving,
    isWaitingFortransferNft,
  };

  if (!isOpen) return null;

  const handleTransfer = (mine: MinePurchased) => {
    setSelectedMine(mine);
    setIsKYCModalOpen(true);
  };

  const handleKYCSubmit = async (data: KYCFormData) => {
    // Convert file to base64
    const fileToBase64 = (file: File): Promise<string> => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
      });
    };

    const base64IdFile = await fileToBase64(data.idFile);

    setUploadData({
      mineName: selectedMine?.mineDetails.name || "",
      walletAddress: address || "0x",
      chainName: chain?.name || "Unknown",
      erc1155VaultAddress,
      txHash: "",
      fullName: data.name,
      email: data.email,
      transferNftNumber: data.transferNftNumber,
      idType: data.idType,
      idFile: base64IdFile,
    });

    handleTransferNft();
  };

  const handleKYCClose = () => {
    setIsKYCModalOpen(false);
    setSelectedMine(null);
  };

  return (
    <>
      <div className={styles.modalOverlay}>
        <div className={styles.modalFrame}>
          <div className={styles.closeButton} onClick={onClose}>
            <img src={crossIcon.src} alt="close" />
          </div>

          <div className={styles.contentWrapper}>
            <div className={styles.titleWrapper}>
              <h1>NFTs to Shares</h1>
              <h2>Select a mine to transfer your NFTs to shares</h2>
            </div>

            <div className={styles.scrollArea}>
              <div className={styles.mineGrid}>
                {eligibleMines.map((mine, index) => (
                  <div key={index} className={styles.mineCard}>
                    <img
                      src={mine.mineDetails.image}
                      alt={mine.mineDetails.name}
                      className={styles.mineImage}
                    />
                    <div className={styles.imageOverlay}></div>
                    <div className={styles.mineCardContent}>
                      <h3 className={styles.mineName}>
                        {mine.mineDetails.name}
                      </h3>
                      <div className={styles.mineDetails}>
                        <p className={styles.mineral}>
                          {mine.mineDetails.mineral}
                        </p>
                        <p className={styles.balance}>
                          Balance:{" "}
                          {mine.tokenDetails.currentUserBalance.toString()}
                        </p>
                      </div>
                      <button
                        className={styles.transferButton}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTransfer(mine);
                        }}
                      >
                        Transfer to Shares
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {eligibleMines.length === 0 && (
              <div className={styles.emptyState}>
                <h3>No eligible mines found</h3>
                <p>
                  You don&apos;t have any mines with real shares available for
                  transfer.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <KYCModal
        isOpen={isKYCModalOpen}
        onClose={handleKYCClose}
        selectedMine={selectedMine}
        walletAddress={address || ""}
        chainName={chain?.name || "Unknown"}
        onSubmit={handleKYCSubmit}
        {...transferNftsProps}
      />
    </>
  );
};

export default TransferModal;
