import { FC, useState } from "react";
import styles from "./TransferModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { MinePurchased } from "../../PurchaseHistory/hooks/usePurchaseHistory";
import KYCModal from "./KYCModal/KYCModal";
import { useAccount, useNetwork } from "wagmi";

interface TransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  eligibleMines: MinePurchased[];
}

interface KYCFormData {
  name: string;
  email: string;
  transferNftNumber: number;
  idType: string;
  idFile: File;
}

const TransferModal: FC<TransferModalProps> = ({
  isOpen,
  onClose,
  eligibleMines,
}) => {
  const [isKYCModalOpen, setIsKYCModalOpen] = useState(false);
  const [selectedMine, setSelectedMine] = useState<MinePurchased | null>(null);
  const { address } = useAccount();
  const { chain } = useNetwork();

  if (!isOpen) return null;

  const handleTransfer = (mine: MinePurchased) => {
    setSelectedMine(mine);
    setIsKYCModalOpen(true);
  };

  const handleKYCSubmit = (data: KYCFormData) => {
    console.log("KYC submitted:", data.idFile);
    console.log("Selected mine:", selectedMine);

    // TODO: Implement actual transfer logic with KYC data
    alert(
      `KYC submitted for ${selectedMine?.mineDetails.name}. Transfer ${data.transferNftNumber} NFTs for ${data.name} (${data.email})`,
    );

    setIsKYCModalOpen(false);
    setSelectedMine(null);
    onClose();
  };

  const handleKYCClose = () => {
    setIsKYCModalOpen(false);
    setSelectedMine(null);
  };

  return (
    <>
      <div className={styles.modalOverlay}>
        <div className={styles.modalFrame}>
          <div className={styles.closeButton} onClick={onClose}>
            <img src={crossIcon.src} alt="close" />
          </div>

          <div className={styles.contentWrapper}>
            <div className={styles.titleWrapper}>
              <h1>NFTs to Shares</h1>
              <h2>Select a mine to transfer your NFTs to shares</h2>
            </div>

            <div className={styles.scrollArea}>
              <div className={styles.mineGrid}>
                {eligibleMines.map((mine, index) => (
                  <div key={index} className={styles.mineCard}>
                    <img
                      src={mine.mineDetails.image}
                      alt={mine.mineDetails.name}
                      className={styles.mineImage}
                    />
                    <div className={styles.imageOverlay}></div>
                    <div className={styles.mineCardContent}>
                      <h3 className={styles.mineName}>
                        {mine.mineDetails.name}
                      </h3>
                      <div className={styles.mineDetails}>
                        <p className={styles.mineral}>
                          {mine.mineDetails.mineral}
                        </p>
                        <p className={styles.balance}>
                          Balance:{" "}
                          {mine.tokenDetails.currentUserBalance.toString()}
                        </p>
                      </div>
                      <button
                        className={styles.transferButton}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTransfer(mine);
                        }}
                      >
                        Transfer to Shares
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {eligibleMines.length === 0 && (
              <div className={styles.emptyState}>
                <h3>No eligible mines found</h3>
                <p>
                  You don&apos;t have any mines with real shares available for
                  transfer.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <KYCModal
        isOpen={isKYCModalOpen}
        onClose={handleKYCClose}
        selectedMine={selectedMine}
        walletAddress={address || ""}
        chainName={chain?.name || "Unknown"}
        onSubmit={handleKYCSubmit}
      />
    </>
  );
};

export default TransferModal;
