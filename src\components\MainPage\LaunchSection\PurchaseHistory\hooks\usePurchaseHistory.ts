import { minesDetails } from "@/constants/mineDetails";
import { useAreaBought } from "./../../AreaBought/hooks/useAreaBought";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useState } from "react";
import { useAccount, useContractReads, useNetwork } from "wagmi";

export type MinePurchased = {
  isPurchased: boolean;
  mineDetails: {
    name: string;
    price: string;
    mineral: string;
    image: string;
    chartDataType: string;
    supportedNetwork: number[];
    manualTotalSupply: number;
    hasRealShares: boolean;
  };
  tokenDetails: {
    totalSupply: bigint;
    soldAmount: bigint;
    currentUserBalance: bigint;
    minAmount: bigint;
    targetFund: bigint;
  };
  tokenSupply: bigint;
};

const usePurchaseHistory = () => {
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [userAddress, setUserAddress] = useState<`0x${string}`>("0x");
  const [assetIds, setAssetIds] = useState({
    mt: 0,
    matsa: 0,
    zephyr: 0,
    jim: 0,
    pcgold: 0,
    menzies: 0,
    hskreward: 0,
  });

  const [areaBought, setAreaBought] = useState<MinePurchased[]>([]);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();

  const { data: tokenSupply } = useContractReads({
    contracts: [
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.mt)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.matsa)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.zephyr)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.jim)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.pcgold)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.menzies)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenSupply",
        args: [BigInt(assetIds.hskreward)],
      },
    ],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      userAddress !== "0x" &&
      assetIds.mt !== 0 &&
      assetIds.matsa !== 0 &&
      assetIds.zephyr !== 0,
  });

  // read the balance of each token to see if the user has purchased it
  // if balance is zero, mean the user has not purchased it
  const { data: purchasedToken } = useContractReads({
    contracts: [
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.mt)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.matsa)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.zephyr)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.jim)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.pcgold)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.menzies)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "balanceOf",
        args: [userAddress, BigInt(assetIds.hskreward)],
      },
    ],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      userAddress !== "0x" &&
      assetIds.mt !== 0 &&
      assetIds.matsa !== 0 &&
      assetIds.zephyr !== 0,
  });

  // read the metadata of each token to calculate the total portion of user
  // owned and remaining
  const { data: asteroidMetadata } = useContractReads({
    contracts: [
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.mt)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.matsa)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.zephyr)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.jim)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.pcgold)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.menzies)],
      },
      {
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "metaAsteroid",
        args: [BigInt(assetIds.hskreward)],
      },
    ],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      assetIds.mt !== 0 &&
      assetIds.matsa !== 0 &&
      assetIds.zephyr !== 0,
  });

  useEffect(() => {
    if (chain?.unsupported || !address) {
      setAsteroidAddress("0x");
      setUserAddress("0x");
      setAssetIds({
        mt: 0,
        matsa: 0,
        zephyr: 0,
        jim: 0,
        pcgold: 0,
        menzies: 0,
        hskreward: 0,
      });
    } else {
      if (chain) {
        setAsteroidAddress(networkConfigs[chain.id].asteroidAddress);
        setAssetIds(networkConfigs[chain.id].assetIds);
      }

      if (address) {
        setUserAddress(address);
      }
    }
  }, [chain, address]);

  useEffect(() => {
    if (purchasedToken && asteroidMetadata && tokenSupply) {
      const tempAreaBought: MinePurchased[] = [];
      minesDetails.map((mine, index) => {
        tempAreaBought.push({
          isPurchased: purchasedToken[index]?.result !== BigInt(0),
          mineDetails: {
            name: mine.name,
            price: mine.minePrice,
            mineral: mine.mineMineral,
            image: mine.mineImages[0],
            chartDataType: mine.chartDataType,
            supportedNetwork: mine.supportedNetwork,
            manualTotalSupply: mine.manualTotalSupply,
            hasRealShares: mine.hasRealShares,
          },
          tokenDetails: {
            totalSupply: asteroidMetadata[index]?.result?.totalSupply ?? 0n,
            soldAmount: asteroidMetadata[index]?.result?.soldAmount ?? 0n,
            currentUserBalance: purchasedToken[index]?.result ?? 0n,
            minAmount: asteroidMetadata[index]?.result?.minAmount ?? 0n,
            targetFund:
              asteroidMetadata[index]?.result?.tokenRaisedAmounts ?? 0n,
          },
          tokenSupply: tokenSupply[index]?.result ?? 0n,
        });
      });

      setAreaBought(tempAreaBought);
    }
  }, [purchasedToken, asteroidMetadata, tokenSupply]);

  return { areaBought };
};

export default usePurchaseHistory;
