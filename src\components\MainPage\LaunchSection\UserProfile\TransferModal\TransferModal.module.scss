@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(10px);
  @include row-center;
  z-index: 9999;

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
  }
}

.modalFrame {
  position: relative;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(20px);
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-md;
  @include col-center;
  padding: $padding-xl;

  .closeButton {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    width: 40px;
    height: 40px;
    @include row-center;
    cursor: pointer;
    border: $border-width-xs solid $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
    transition: all 0.2s ease;

    &:hover {
      background: $color-primary-contrast;
      border-color: $color-primary;
    }

    img {
      width: 20px;
      height: 20px;
    }
  }
}

.contentWrapper {
  width: 100%;
  height: 100%;
  @include col-center;
  justify-content: flex-start;
  gap: $spacing-xl;
}

.titleWrapper {
  text-align: center;
  line-height: 127.5%;

  h1 {
    font-size: $font-size-3xl;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    color: $color-primary;
    margin-bottom: $spacing-sm;
  }

  h2 {
    font-size: $font-size-lg;
    font-weight: $font-weight-light;
    color: $color-primary-transparent-contrast;
  }
}

.scrollArea {
  width: 100%;
  flex: 1;
  overflow-y: auto;
  padding: $spacing-md 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: $color-black-transparent-light;
    border-radius: $border-radius-sm;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;

    &:hover {
      background: $color-primary;
    }
  }
}

.mineGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: $spacing-lg;
  padding: $spacing-md;
}

.mineCard {
  position: relative;
  height: 250px;
  border: $border-width-xs solid $color-primary-contrast;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    border-color: $color-primary;
    transform: translateY(-5px);

    .transferButton {
      background: $color-primary;
      color: $color-black-transparent-dark;
    }
  }
}

.mineImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.8),
    rgba(0, 0, 0, 0.8)
  );
  z-index: 2;
}

.mineCardContent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: $padding-lg;
  @include col-center;
  align-items: flex-start;
  gap: $spacing-sm;
  z-index: 3;
}

.mineName {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  color: $color-primary;
  text-transform: uppercase;
  margin: 0;
}

.mineDetails {
  width: 100%;
  @include col-center;
  align-items: flex-start;
  gap: $spacing-xs;
}

.mineral {
  font-size: $font-size-sm;
  color: $color-primary-transparent-contrast;
  font-weight: $font-weight-medium;
  text-transform: uppercase;
  margin: 0;
}

.balance {
  font-size: $font-size-sm;
  color: #027b81;
  font-weight: $font-weight-medium;
  margin: 0;
}

.transferButton {
  @include row-center;
  width: 100%;
  padding: $padding-sm $padding-md;
  font-size: $font-size-sm;
  font-family: $font-family-poppins;
  font-weight: $font-weight-semibold;
  color: $color-primary;
  text-transform: uppercase;
  background: $color-primary-transparent;
  border: $border-width-xs solid $color-primary-contrast;
  border-radius: $border-radius-sm;
  backdrop-filter: blur(5px);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: $spacing-sm;

  &:hover {
    border-color: $color-primary;
    background: $color-primary-transparent-contrast;
  }

  &:active {
    transform: scale(0.98);
  }
}

.emptyState {
  @include col-center;
  gap: $spacing-md;
  text-align: center;
  padding: $padding-2xl;

  h3 {
    font-size: $font-size-xl;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
  }

  p {
    font-size: $font-size-md;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modalFrame {
    width: 95vw;
    height: 95vh;
    padding: $padding-lg;
  }

  .mineGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .titleWrapper h1 {
    font-size: $font-size-2xl;
  }

  .titleWrapper h2 {
    font-size: $font-size-md;
  }
}
