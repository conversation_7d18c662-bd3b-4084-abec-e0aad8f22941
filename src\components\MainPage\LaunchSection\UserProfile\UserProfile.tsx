import styles from "./UserProfile.module.scss";
import locationIcon from "@/assets/icons/plusModal/locationIcon.png";
import nativeBalanceIcon from "@/assets/icons/plusModal/nativeBalanceIcon.png";
import usdtBalanceIcon from "@/assets/icons/plusModal/usdtBalanceIcon.png";
import ethIcon from "@/assets/icons/plusModal/ethIcon.png";
import { useAccount, useNetwork } from "wagmi";
import Link from "next/link";
import { getRandomAvatar } from "@/constants/avatarsList";
import useUserBalance from "./hooks/useUserBalance";
import { formatEther } from "viem";
import { useMemo, useState } from "react";
import usePurchaseHistory from "../PurchaseHistory/hooks/usePurchaseHistory";
import TransferModal from "./TransferModal/TransferModal";

const UserProfile = () => {
  const { areaBought } = usePurchaseHistory();
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false);

  const eligibleMines = areaBought.filter(
    (area) => area.isPurchased && area.mineDetails.hasRealShares,
  );
  const isShowTransferButton = eligibleMines.length > 0;

  const { isConnected, address } = useAccount();
  const { nativeBalance, usdtBalance, usdtDecimals } = useUserBalance();
  const { chain } = useNetwork();

  const avatar = useMemo(() => getRandomAvatar(), []);

  const trimWalletAddress = (address: string) =>
    address.slice(0, 6) + "..." + address.slice(-4);

  const SpacerY = () => <div className={styles.spacingY} />;

  return (
    <>
      {isConnected && !chain?.unsupported && address ? (
        <div className={styles.userProfile}>
          <div className={styles.titleWrapper}>
            <h1>user profile</h1>
            <h2>area bought</h2>
          </div>
          <SpacerY />
          <div className={styles.avatar}>
            {/* <h1 className={styles.letter}>C</h1> */}
            <img
              src={avatar.image}
              alt="avatar"
              width={"100%"}
              height={"100%"}
            />
          </div>
          <SpacerY />
          <div className={styles.infoWrapper}>
            <h1>administer</h1>
            {/* <div className={styles.subtitle}>
              <img
                src={locationIcon.src}
                alt="location icon"
                width={16}
                height={16}
              />
              <h2>null</h2>
            </div> */}
            <div className={styles.subtitle}>
              <img src={ethIcon.src} alt="eth icon" width={14} />
              <h2>{trimWalletAddress(address.toString())}</h2>
            </div>
            <div className={styles.subtitle}>
              <img src={nativeBalanceIcon.src} alt="eth icon" width={14} />
              <h2>
                {Number(formatEther(nativeBalance?.value ?? 0n)).toFixed(2)}{" "}
                {nativeBalance?.symbol}
              </h2>
            </div>
            <div className={styles.subtitle}>
              <img src={usdtBalanceIcon.src} alt="eth icon" width={14} />
              <h2>
                {Number(usdtBalance / 10n ** BigInt(usdtDecimals)).toFixed(2)}{" "}
                USDT
              </h2>
            </div>
            {isShowTransferButton ? (
              <div className={styles.transferButton}>
                <button onClick={() => setIsTransferModalOpen(true)}>
                  NFTs to Shares
                </button>
              </div>
            ) : null}
            {/* <Link
              href="/statistics"
              replace
              style={{ color: "yellow", fontSize: "1.2rem" }}
            >
              Point Systems
            </Link> */}
          </div>
        </div>
      ) : null}

      <TransferModal
        isOpen={isTransferModalOpen}
        onClose={() => setIsTransferModalOpen(false)}
        eligibleMines={eligibleMines}
      />
    </>
  );
};

export default UserProfile;
