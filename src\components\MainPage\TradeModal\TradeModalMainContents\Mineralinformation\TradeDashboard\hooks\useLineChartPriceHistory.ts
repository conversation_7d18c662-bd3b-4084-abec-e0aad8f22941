import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";

type TPriceHistory = {
  date: string;
  price: string;
  timestamp: number;
  paymentToken: `0x${string}`;
};

type TFloorPrice = {
  current: string;
  change24h: string;
  percentage24h: string;
  isAllTime: boolean;
};

const useLineChartPriceHistory = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [priceHistory, setPriceHistory] = useState<TPriceHistory[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [floorPrice, setFloorPrice] = useState<TFloorPrice>({
    current: "0",
    change24h: "0",
    percentage24h: "0",
    isAllTime: false,
  });
  const [isLoadingFloor, setIsLoadingFloor] = useState(true);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const OFFSET = 0n;
  const LIMIT = 30n;

  // Function to convert Unix timestamp to formatted date
  const convertUnixTimeToDate = (unixTime: string): string => {
    const date = new Date(Number(unixTime) * 1000);
    return date.toLocaleDateString();
  };

  // Function to calculate 24h floor price change
  const calculateFloorPriceFromHistory = (priceHistory: TPriceHistory[]) => {
    if (priceHistory.length === 0) {
      return {
        current: "0",
        change24h: "0",
        percentage24h: "0",
        isAllTime: false,
      };
    }

    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    const prices24h = priceHistory.filter((record) => {
      return record.timestamp * 1000 >= oneDayAgo;
    });

    // If no 24h data, fallback to all-time floor price
    if (prices24h.length === 0) {
      const allTimeFloor = priceHistory.reduce((min, current) => {
        return BigInt(current.price) < BigInt(min.price) ? current : min;
      });

      return {
        current: allTimeFloor.price,
        change24h: "0",
        percentage24h: "0",
        isAllTime: true,
      };
    }

    // Find the minimum price in the last 24 hours as the current floor price
    const currentFloorPrice = prices24h.reduce((min, current) => {
      return BigInt(current.price) < BigInt(min.price) ? current : min;
    });

    // Find the floor price 24 hours ago for comparison
    const twoDaysAgo = Date.now() - 48 * 60 * 60 * 1000;
    const prices24hTo48h = priceHistory.filter((record) => {
      const timestamp = record.timestamp * 1000;
      return timestamp >= twoDaysAgo && timestamp < oneDayAgo;
    });

    let previousFloorPrice = "0";
    if (prices24hTo48h.length > 0) {
      const previousFloor = prices24hTo48h.reduce((min, current) => {
        return BigInt(current.price) < BigInt(min.price) ? current : min;
      });
      previousFloorPrice = previousFloor.price;
    }

    const current = BigInt(currentFloorPrice.price);
    const previous = BigInt(previousFloorPrice);
    const change = current - previous;
    const percentage =
      previous > 0n ? Number((change * 10000n) / previous) / 100 : 0;

    return {
      current: currentFloorPrice.price,
      change24h: change.toString(),
      percentage24h: percentage.toFixed(2),
      isAllTime: false,
    };
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getPriceHistoryPaginated",
    args: [BigInt(asteroidMineNumber), OFFSET, LIMIT],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
    onSuccess: (data) => {
      const tempPriceHistory = data[0].map((price) => ({
        date: convertUnixTimeToDate(price.timestamp.toString()),
        price: price.price.toString(),
        timestamp: Number(price.timestamp),
        paymentToken: price.paymentToken as `0x${string}`,
      }));
      tempPriceHistory.sort((a, b) => a.timestamp - b.timestamp);
      setPriceHistory(tempPriceHistory);

      // Calculate floor price from price history
      const floorPriceData = calculateFloorPriceFromHistory(tempPriceHistory);
      setFloorPrice(floorPriceData);
      setIsLoadingFloor(false);
      setIsLoadingHistory(false);
    },
    onError: () => {
      setIsLoadingHistory(false);
      setIsLoadingFloor(false);
    },
  });

  return {
    priceHistory,
    isLoadingHistory,
    floorPrice,
    isLoadingFloor,
  };
};

export default useLineChartPriceHistory;
