import { useState } from "react";
import PriceHistoryGraph from "./PriceHistoryGraph";
import FloorPrice from "./FloorPrice";
import styles from "./TradeDashboard.module.scss";

const PriceHistory = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [range, setRange] = useState<"24H" | "7D" | "30D">("30D");
  return (
    <div className={styles.priceHistory}>
      <div
        className={styles.headerContainer}
        onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className={styles.mainHeader}>PRICE HISTORY</h2>
        <span className={styles.toggleIcon}>{isOpen ? "-" : "+"}</span>
      </div>
      {isOpen && (
        <>
          <FloorPrice />
          <div className={styles.rangeTabs}>
            {(["24H", "7D", "30D"] as const).map((label) => (
              <button
                key={label}
                className={`${styles.rangeButton} ${
                  range === label ? styles.active : ""
                }`}
                onClick={() => setRange(label)}
                type="button"
              >
                {label}
              </button>
            ))}
          </div>
          <div className={styles.graph}>
            <PriceHistoryGraph range={range} />
          </div>
        </>
      )}
    </div>
  );
};

export default PriceHistory;
