import {
  CameraCont<PERSON>s,
  Float,
  Loader,
  OrbitControls,
  <PERSON>roll,
  ScrollControls,
  Stars,
  useScroll,
  useProgress,
} from "@react-three/drei";
import { Canvas, useFrame } from "@react-three/fiber";
import StarsScene from "./Scenes/StarsScene";
import styles from "./Experience.module.scss";
import { Ref, Suspense, useLayoutEffect, useRef, useState } from "react";
import MainTitle from "./Html/MainTitle/MainTitle";
import MinesInformation from "./Html/MinesInformation/MinesInformation";
import TeamsInformation from "./Html/TeamsInformation/TeamsInformation";
import ProjectRoadmap from "./Html/ProjectRoadmap/ProjectRoadmap";
import AsteroidScene from "./Scenes/AsteroidScene";
import Lighting from "./Scenes/Lighting";
import AutoRotate from "./Scenes/AutoRotate";
import GalaxyScene from "./Scenes/GalaxyScene";
import SparklesScene from "./Scenes/SparklesScene";
import CloudScene from "./Scenes/CloudScene";
import { universeAnimationStore } from "@/stores/universeAnimation";
import { useSnapshot } from "valtio";
import { useInView } from "react-intersection-observer";
import Navigation from "./Html/Navigation/Navigation";
import { useRouter } from "next/navigation";
import { useMediaQuery } from "usehooks-ts";
import EarthScene from "./Scenes/EarthScene";
import { scrollContainerStore } from "@/stores/scrollContainer";
import ElectronsScene from "./Scenes/ElectronsScene";
import MilkyWayScene from "./Scenes/MilkyWayScene";
import NeonRingScene from "./Scenes/NeonRingScene";
import {
  Bloom,
  EffectComposer,
  ToneMapping,
} from "@react-three/postprocessing";
import { ToneMappingMode } from "postprocessing";

const Experience = () => {
  const cameraControlsRef = useRef<CameraControls>();
  const universeAnimationSnapshot = useSnapshot(universeAnimationStore);
  const scrollContainerSnapshot = useSnapshot(scrollContainerStore);
  const [enableScroll, setEnableScroll] = useState(false);
  const { replace } = useRouter();
  const { progress: modelLoadingProgress } = useProgress();

  const { inView: minesInformationInView, ref: minesInformationRef } =
    useInView({ threshold: 0.01 });
  const is2240 = useMediaQuery("(min-width: 2240px)");
  const is1920 = useMediaQuery("(min-width: 1920px)");
  const is1600 = useMediaQuery("(min-width: 1600px)");
  const is1280 = useMediaQuery("(min-width: 1280px)");
  const isTablet = useMediaQuery("(min-width: 768px)");
  const isMobile = useMediaQuery("(max-width: 767px)");
  // const isMobileTablet = useMediaQuery("(max-width: 1279px)");
  const getNumberOfPages = () => {
    if (is2240) return 6;
    if (is1920) return 7;
    if (is1600) return 8;
    if (is1280) return 9;
    if (isTablet) return 9;
    if (isMobile) return 9;
    // if (isMobileTablet) return 9;

    return 9;
  };

  const INITIAL_SCALE_FACTOR = 0.06;
  const [numberOfPages, setNumberOfPages] = useState(getNumberOfPages());
  const [scaleFactor, setScaleFactor] = useState(INITIAL_SCALE_FACTOR);

  useLayoutEffect(() => {
    if (modelLoadingProgress === 100) {
      setTimeout(() => {
        setEnableScroll(true);
      }, 5000);
    }
  }, [modelLoadingProgress]);

  return (
    <>
      <Canvas
        camera={{ position: [0, 0, 1000], fov: 75 }}
        gl={{ preserveDrawingBuffer: true }}
        className={styles.canvas}
      >
        <EffectComposer disableNormalPass>
          <>
            {!universeAnimationSnapshot.hasInitialAnimationDone ? (
              <>
                <Bloom
                  mipmapBlur
                  luminanceThreshold={1}
                  levels={8}
                  intensity={15}
                />
                {/* <ToneMapping mode={ToneMappingMode.ACES_FILMIC} /> */}
              </>
            ) : null}
          </>
        </EffectComposer>

        <CameraControls
          ref={cameraControlsRef as Ref<CameraControls>}
          touches={
            isMobile || isTablet ? { one: 0, two: 0, three: 0 } : undefined
          }
          mouseButtons={
            isMobile || isTablet
              ? { wheel: 0, left: 0, right: 0, middle: 0 }
              : { wheel: 0, left: 1, right: 1, middle: 1 }
          }
        />
        <AutoRotate cameraControlsRef={cameraControlsRef} />
        <Lighting />
        <ScrollControls
          pages={numberOfPages}
          damping={0}
          enabled={enableScroll}
        >
          <Suspense fallback={null}>
            {/* <MilkyWayScene /> */}
            {/* <CloudScene /> */}
            <SparklesScene />
            <GalaxyScene />
            <EarthScene />
            <ElectronsScene />
            <AsteroidScene
              cameraControlsRef={cameraControlsRef}
              replace={replace}
              scaleFactor={scaleFactor}
            />
            {!universeAnimationSnapshot.hasInitialAnimationDone ? (
              <>
                {/* <NeonRingScene /> */}
                <StarsScene />
              </>
            ) : null}
          </Suspense>
          <Scroll html>
            <MainTitle
              cameraControlsRef={cameraControlsRef}
              minesInformationInView={minesInformationInView}
              minesInformationRef={minesInformationRef}
              replace={replace}
              numberOfPages={numberOfPages}
            />
            {universeAnimationSnapshot.hasInitialAnimationDone &&
            !universeAnimationSnapshot.showFinalAnimation ? (
              <Navigation
                cameraControlsRef={cameraControlsRef}
                setScaleFactor={setScaleFactor}
                INITIAL_SCALE_FACTOR={INITIAL_SCALE_FACTOR}
              />
            ) : null}
            {/* <MinesInformation minesInformationRef={minesInformationRef} /> */}
            {/* <TeamsInformation />
            <ProjectRoadmap /> */}
          </Scroll>
        </ScrollControls>
      </Canvas>
      <Loader />
    </>
  );
};

export default Experience;
