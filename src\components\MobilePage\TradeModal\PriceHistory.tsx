import PriceHistoryGraph from "./PriceHistoryGraph";
import FloorPrice from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/FloorPrice";
import styles from "./CurrentPrice.module.scss";
import { useState } from "react";

const PriceHistory = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [range, setRange] = useState<"24H" | "7D" | "30D">("30D");

  const handleToggle = () => {
    setIsExpanded((prevState) => !prevState);
  };
  return (
    <div className={styles.container}>
      <div className={styles.priceHistory}>
        <div
          className={`${styles.mainHeader} ${
            isExpanded ? styles.expanded : styles.collapsed
          }`}
          onClick={handleToggle}
        >
          <span className={styles.glowText}>PRICE HISTORY</span>
          <span className={styles.buttonHighlight}></span>
        </div>
        {isExpanded && (
          <>
            <FloorPrice />
            <div className={styles.rangeTabs}>
              {(["24H", "7D", "30D"] as const).map((label) => (
                <button
                  key={label}
                  className={`${styles.rangeButton} ${
                    range === label ? styles.active : ""
                  }`}
                  onClick={() => setRange(label)}
                  type="button"
                >
                  {label}
                </button>
              ))}
            </div>
            <div className={styles.graph}>
              <PriceHistoryGraph />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PriceHistory;
