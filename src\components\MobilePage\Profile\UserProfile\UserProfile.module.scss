@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$avatar-size: 189px;

@mixin avatar {
  width: $avatar-size;
  height: $avatar-size;
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-sm;
  @include row-center;
  // cursor: pointer;

  .letter {
    font-size: $font-size-6xl;
    font-weight: $font-weight-light;
    color: #00f1ff;
    text-transform: uppercase;
  }
}

.spacingY {
  margin: $margin-md 0;
}

.userProfile {
  width: 100%;
  // height: 100%;
  // border: 1px solid yellow;

  .avatarWrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    // border: 1px solid yellow;

    .avatar {
      @include avatar();
    }
  }

  .infoWrapper {
    @include col-center;
    align-items: flex-start;
    // line-height: 127.5%;
    gap: $spacing-sm;

    h1 {
      font-size: $font-size-3xl;
      font-weight: $font-weight-medium;
      color: $color-warning;
      text-transform: uppercase;
    }
    .subtitle {
      width: 100%;
      @include row-between;
      justify-content: flex-start;
      gap: $spacing-sm;

      h2 {
        font-size: $font-size-md;
        color: #027b81;
        font-weight: $font-weight-medium;
      }
    }

    .transferButton {
      width: 100%;

      button {
        @include row-center;
        width: 100%;
        text-align: center;
        font-size: $font-size-sm;
        font-family: $font-family-poppins;
        color: #00ffff;
        text-transform: uppercase;
        font-weight: $font-weight-semibold;
        border: $border-width-xs solid $color-primary;
        padding: $padding-sm 0;
        margin: 0.5rem 0;
        background: $color-primary-transparent;
        border-radius: $border-radius-sm;
        cursor: pointer;

        span {
          color: $color-primary-transparent-contrast;
        }

        &:hover {
          border: $border-width-xs solid $color-primary;
        }
      }
    }
  }
}
