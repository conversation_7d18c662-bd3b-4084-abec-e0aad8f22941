@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$avatar-size: 189px;

@mixin avatar {
  width: $avatar-size;
  height: $avatar-size;
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-sm;
  @include row-center;
  // cursor: pointer;

  .letter {
    font-size: $font-size-6xl;
    font-weight: $font-weight-light;
    color: #00f1ff;
    text-transform: uppercase;
  }
}

.spacingY {
  margin: $margin-md 0;
}

.userProfile {
  width: 100%;
  // height: 100%;
  // border: 1px solid yellow;

  .avatarWrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    // border: 1px solid yellow;

    .avatar {
      @include avatar();
    }
  }

  .infoWrapper {
    @include col-center;
    align-items: flex-start;
    // line-height: 127.5%;
    gap: $spacing-sm;

    h1 {
      font-size: $font-size-3xl;
      font-weight: $font-weight-medium;
      color: $color-warning;
      text-transform: uppercase;
    }
    .subtitle {
      width: 100%;
      @include row-between;
      justify-content: flex-start;
      gap: $spacing-sm;

      h2 {
        font-size: $font-size-md;
        color: #027b81;
        font-weight: $font-weight-medium;
      }
    }

    .transferButton {
      width: 100%;
      margin-top: $spacing-md;

      button {
        width: 100%;
        padding: $padding-md;
        background: $color-primary;
        color: $color-black-transparent-dark;
        border: none;
        border-radius: $border-radius-sm;
        font-size: $font-size-md;
        font-weight: $font-weight-semibold;
        text-transform: uppercase;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 44px; // Better touch target for mobile

        &:hover {
          background: $color-primary-contrast;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0) scale(0.98);
        }
      }
    }
  }
}
