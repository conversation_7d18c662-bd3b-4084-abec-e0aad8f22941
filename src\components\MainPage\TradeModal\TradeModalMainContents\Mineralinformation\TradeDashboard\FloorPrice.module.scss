@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

// Floor Price Styles
.floorPriceContainer {
  padding: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba($color-primary, 0.1);
}

.floorPriceHeader {
  h3 {
    color: $color-primary;
    font-size: 0.875rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.floorPriceLoader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.loader {
  width: 16px;
  height: 16px;
  border: 1px solid #fff;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.floorPriceInfo {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.currentFloorPrice {
  .floorPriceAmount {
    color: $color-primary;
    font-size: 1.5rem;
    font-weight: bold;
    display: block;
  }
}

.floorPriceChange {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .changeAmount,
  .changePercentage {
    font-size: 0.875rem;
    font-weight: 500;
  }

  .positive {
    color: #4caf50;
  }

  .negative {
    color: #f44336;
  }
}

.changeAmount {
  &.positive::before {
    content: "↗ ";
  }

  &.negative::before {
    content: "↘ ";
  }
}
