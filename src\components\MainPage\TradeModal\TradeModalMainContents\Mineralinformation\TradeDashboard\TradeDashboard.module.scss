@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$hightlightBackground: rgba($color-primary, 0.3);

.container {
  width: 100%;

  .mainHeader {
    color: $color-primary;
    font-size: 1rem;
    // margin-bottom: 1rem;
    padding: 1rem;
    // border-bottom: 1px solid rgba($color-primary, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    // background: $color-primary-transparent;

    // &::after {
    //   content: "-";
    //   font-size: 1.5rem;
    // }
  }
}

.detailsWrapper {
  width: 100%;
  @include row-between;
}

.buttonWrapper {
  width: 100%;
  @include row-between;
  gap: 1rem;
}

.currentPrice {
  background: rgba($color-black-transparent-light, 0.05);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.priceActions {
  padding: 1rem;
}

.buttons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  // gap: 0.5rem;
  margin-bottom: 1rem;

  .buttonsGroup {
    display: flex;
    gap: 0.5rem;
  }

  button {
    padding: 0.5rem 1.5rem;
    // border: none;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
  }
}
.connectButton {
  justify-self: flex-end;
}

.sellButton {
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;
  padding: 0.75rem 1.5rem;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.buyButton {
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;
  @include row-center;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.priceInfo {
  width: 100%;
  //   margin-bottom: 1.5rem;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: $color-primary-contrast;
  font-size: 0.875rem;
  //   margin-bottom: 0.5rem;
}

.price {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;

  .amount {
    color: $color-primary;
    font-size: 2rem;
    font-weight: bold;
  }

  .usdPrice {
    color: $color-primary-contrast;
  }
}

.actions {
  width: 100%;
  @include col-between;
  align-items: flex-end;
  gap: 1rem;
}

.maxPrice {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  span {
    color: $color-primary;
  }

  input {
    width: 180px;
    background: $hightlightBackground;
    border: 1px solid rgba($color-primary, 0.3);
    color: $color-primary;
    padding: 0.5rem;
    border-radius: 4px;
  }
}

.buyNowButton {
  width: 100%;
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.makeOfferButton {
  width: 100%;
  background: $color-black-transparent-dark;
  color: $color-primary;
  padding: 0.75rem 1.5rem;
  border: 1px solid $color-primary;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  span {
    color: $color-primary;
  }

  button {
    background: $color-black-transparent-dark;
    color: white;
    border: 1px solid rgba($color-primary, 0.3);
    width: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      color: $color-primary;
      background: $hightlightBackground;
    }

    &:disabled {
      background: $color-black-transparent-dark;
      color: $color-primary-contrast;
      border: 1px solid $color-primary-contrast;
      cursor: not-allowed;
    }
  }
}

.priceHistory {
  background: rgba($color-black-transparent-light, 0.05);
  border-radius: 8px;
  margin-bottom: 1rem;

  .rangeTabs {
    display: flex;
    gap: 0.5rem;
    padding: 0 1rem 0.5rem 1rem;
  }

  .rangeButton {
    border: 1px solid $color-primary;
    background: $color-black-transparent-dark;
    color: $color-primary;
    padding: 0.4rem 0.9rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    font-family: $font-family-orbitron;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: $font-size-sm;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &.active {
      background: rgba($color-primary, 0.25);
      font-weight: 700;
    }
  }

  .graph {
    height: 400px;
    padding: 1rem;
    // Graph styling would go here
  }
}

.listings {
  background: rgba($color-black-transparent-light, 0.05);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.listingsTable {
  padding: 0 1rem 1rem;

  .tableHeader {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 100px;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba($color-black-transparent-light, 0.1);
    color: rgba(white, 0.6);
    font-size: 0.875rem;
  }

  .tableBody {
    max-height: calc(5 * 3.5rem); /* Adjust the row height (3.5rem) as needed */
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    .tableRow {
      height: 3.5rem; /* Set a fixed height for each row */
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 100px;
      gap: 1rem;
      padding: 1rem 0;
      border-bottom: 1px solid rgba($color-black-transparent-light, 0.1);
      align-items: center;

      &:last-child {
        border-bottom: none;
      }

      .price {
        color: $color-primary;
      }

      .usdPrice {
        color: $color-primary;
      }

      .quantity {
        color: $color-primary;
      }

      .expiration {
        color: $color-primary;
      }

      .from {
        color: $color-primary;
      }

      button.buyButton {
        padding: 0.5rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        width: 100%;
        font-weight: bold;
        border: 1px solid $color-primary;
        background: $color-black-transparent-dark;
        color: $color-primary;

        &:hover {
          background: $hightlightBackground;
        }
      }
    }
  }
}

.buyWrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.autoComplete {
  width: 250px;
  height: 35px;
  padding: 0 8px;
  border-radius: 4px;
  border: 1px solid $color-primary;
  background-color: transparent;
  color: $color-primary;
  font-size: 14px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $color-primary;
  }

  option {
    background-color: #fff;
    color: #000;
  }
}
.loader {
  width: 16px;
  height: 16px;
  border: 1px solid #fff;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Add these styles to your existing SCSS file

.headerContainer {
  background: $color-primary-transparent;
  border-bottom: 1px solid rgba($color-primary, 0.1);
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 1rem 0;
}

.toggleIcon {
  margin-right: 1rem;
  font-size: 20px;
  font-weight: bold;
}

.pulseButton {
  position: relative;
  overflow: hidden;
  animation: pulse 2s infinite;
  box-shadow: 0 0 10px rgba(0, 224, 255, 0.5);
  border: 1px solid rgba(0, 224, 255, 0.8) !important;
  transition: all 0.3s ease;
  padding: 0.75rem 1.5rem;

  &:hover {
    animation: none;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.8);
  }

  &:disabled {
    animation: none;
    box-shadow: none;
  }
}

.glowText {
  position: relative;
  z-index: 2;
  background: linear-gradient(90deg, #00e0ff, #ffffff, #00e0ff);
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shine 3s linear infinite;
}

.buttonHighlight {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 224, 255, 0.4),
    transparent
  );
  animation: sweep 3s infinite;
  z-index: 1;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 5px rgba(0, 224, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 224, 255, 0.5);
  }
}

@keyframes shine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes sweep {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}
