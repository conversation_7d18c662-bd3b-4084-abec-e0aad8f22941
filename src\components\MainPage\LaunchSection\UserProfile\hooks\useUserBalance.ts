import { useLayoutEffect, useState } from "react";
import { useAccount, useBalance, useContractRead, useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";
import { usdtABI } from "@/constants/abis/UsdtABI";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import useMineTokenId from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useMineTokenId";

const useUserBalance = () => {
  const { isConnected, address } = useAccount();
  const { data: nativeBalance } = useBalance({
    address,
    watch: true,
    enabled: isConnected && address !== undefined,
  });
  const { chain } = useNetwork();
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [usdtDecimals, setUsdtDecimals] = useState<number>(18);
  const [avaliableNfts, setAvaliableNfts] = useState(0);
  const [usdtBalance, setUsdtBalance] = useState(0n);
  const { getSelectedMineId } = useMineTokenId();

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setUsdtAddress(networkConfigs[chain.id].usdtAddress);
      setAsteroidAddress(networkConfigs[chain.id].asteroidAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setUsdtAddress("0x");
      setAsteroidAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, getSelectedMineId]);

  useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "balanceOf",
    args: [address ?? "0x", BigInt(asteroidMineNumber)],
    enabled: isConnected && asteroidAddress !== "0x" && address !== undefined,
    watch: true,
    onSuccess: (data) => {
      setAvaliableNfts(Number(data));
    },
  });

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "decimals",
    enabled: isConnected && usdtAddress !== "0x",
    onSuccess: (data) => {
      setUsdtDecimals(Number(data));
    },
    onError: () => {
      setUsdtDecimals(18);
    },
  });

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "balanceOf",
    args: [address ?? "0x"],
    enabled: isConnected && usdtAddress !== "0x",
    onSuccess: (data) => {
      setUsdtBalance(data);
    },
    onError: () => {
      setUsdtBalance(0n);
    },
  });
  return { nativeBalance, usdtBalance, usdtDecimals, avaliableNfts };
};

export default useUserBalance;
