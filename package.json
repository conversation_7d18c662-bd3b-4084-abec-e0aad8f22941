{"name": "astro-frontend", "version": "0.1.0", "private": true, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "deploy": "next start -p 3030", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@ducanh2912/next-pwa": "^10.1.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-tooltip": "^1.0.6", "@react-three/drei": "9.68.3", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.19.1", "@types/node": "20.4.2", "@types/react": "18.2.15", "@types/react-dom": "18.2.7", "eslint": "8.45.0", "eslint-config-next": "13.4.10", "ethers": "^6.13.1", "framer-motion": "^10.13.0", "moment": "^2.29.4", "next": "14.2.31", "rc-slider": "^11.1.5", "react": "18.2.0", "react-alice-carousel": "^2.9.1", "react-confetti": "^6.1.0", "react-countdown": "^2.3.6", "react-countdown-circle-timer": "^3.2.1", "react-countup": "^6.5.0", "react-device-detect": "^2.2.3", "react-dom": "18.2.0", "react-google-charts": "^4.0.7", "react-hook-form": "^7.52.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.2", "react-pannellum": "0.2.14", "react-parallax-tilt": "^1.7.170", "react-swipeable": "^7.0.1", "react-toastify": "^10.0.6", "react-type-animation": "^3.1.0", "react-vertical-timeline-component": "^3.6.0", "recharts": "^2.15.4", "server-only": "^0.0.1", "sharp": "^0.34.3", "three": "0.148.0", "typescript": "5.1.6", "usehooks-ts": "^2.16.0", "valtio": "^1.13.2", "viem": "^1.21.4", "wagmi": "^1.4.13", "zod": "^3.25.76"}, "devDependencies": {"@types/react-vertical-timeline-component": "^3.3.6", "@types/three": "^0.155.1", "eslint-config-prettier": "^8.10.2", "husky": "^9.1.7", "lint-staged": "^13.3.0", "prettier": "3.0.0", "sass": "^1.89.2"}}