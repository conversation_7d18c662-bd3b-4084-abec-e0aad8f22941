@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.fileUploadWrapper {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  width: 100%;
}

.label {
  font-family: $font-family-poppins;
  font-size: $font-size-xs;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.required {
  color: $color-danger;
}

.dropZone {
  padding: $spacing-xl;
  border: $border-width-xs dashed $color-primary-transparent-contrast;
  border-radius: $border-radius-sm;
  background: $color-black-transparent-medium;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;

  &:hover {
    border-color: $color-primary;
    background: $color-black-transparent-light;
  }

  &.error {
    border-color: $color-danger;
    background: rgba(246, 70, 93, 0.1);
  }
}

.hiddenInput {
  display: none;
}

.dropContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-sm;
}

.uploadIcon {
  font-size: 24px;
  opacity: 0.6;
}

.uploadText {
  font-family: $font-family-poppins;
  font-size: $font-size-sm;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.uploadSubtext {
  font-family: $font-family-poppins;
  font-size: $font-size-xs;
  color: rgba(255, 255, 255, 0.5);
}

.fileSelected {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xs;
}

.fileName {
  font-family: $font-family-poppins;
  font-size: $font-size-sm;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.fileSize {
  font-family: $font-family-poppins;
  font-size: $font-size-xs;
  color: rgba(255, 255, 255, 0.6);
}

.errorMessage {
  font-family: $font-family-poppins;
  font-size: $font-size-xs;
  color: $color-danger;
  margin-top: -4px;
}
