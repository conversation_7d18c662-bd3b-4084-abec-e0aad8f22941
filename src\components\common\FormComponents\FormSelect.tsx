import { FC, SelectHTMLAttributes } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import styles from "./FormSelect.module.scss";

interface FormSelectProps extends SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  error?: string;
  register: UseFormRegisterReturn;
  required?: boolean;
  options: { value: string; label: string }[];
  placeholder?: string;
}

const FormSelect: FC<FormSelectProps> = ({
  label,
  error,
  register,
  required,
  options,
  placeholder,
  ...props
}) => {
  return (
    <div className={styles.selectWrapper}>
      <label className={styles.label}>
        {label}
        {required && <span className={styles.required}>*</span>}
      </label>
      <select
        {...register}
        {...props}
        className={`${styles.select} ${error ? styles.error : ""}`}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && <span className={styles.errorMessage}>{error}</span>}
    </div>
  );
};

export default FormSelect;
