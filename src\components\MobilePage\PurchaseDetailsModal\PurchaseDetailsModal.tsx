import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import styles from "./PurchaseDetailsModal.module.scss";
import { useSnapshot } from "valtio";
import { motion } from "framer-motion";
import { buttonEffect, fadeIn } from "@/animations/animations";
import { useEffect, useLayoutEffect, useState, useRef } from "react";
import { formatEther, parseEther } from "viem";
import { useAccount, useFeeData, useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";
import variables from "@/styles/variables.module.scss";
import FaucetModal from "./FaucetModal/FaucetModal";
import { useMediaQuery } from "usehooks-ts";
import doubleRing from "@/assets/icons/bottomNavigation/doubleRing.svg";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import TransactionDetails from "./TransactionDetails/TransactionDetails";
import Image from "next/image";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { navbarButtonStore } from "@/stores/navbarButton";
import Slider from "rc-slider";
import "rc-slider/assets/index.css";
import soldoutImage from "@/components/MainPage/PurchaseDetailsModal/assets/images/completedImage.png";
import informationIcon from "@/components/MainPage/PurchaseDetailsModal/assets/images/informationIcon.png";
import * as Tooltip from "@radix-ui/react-tooltip";

type PurchaseDetailsModalProps = {
  purchaseNft: () => void;
  purchaseSpecialEvent: () => void;
  allowanceBalance: bigint | undefined;
  approveUsdt: () => void;
  approveZeroUsdt: () => void;
  minePrice: bigint | undefined;
  unitPriceFromContract?: bigint | undefined;
  loadingType: {
    isApproving: boolean;
    isWaitingForApprove: boolean;
    isApprovingZero: boolean;
    isWaitingForApproveZero: boolean;
    isWaitingForConfirmation: boolean;
    isMintingNft: boolean;
    isSpecialEventLoading: boolean;
    isWaitingForSpecialEvent: boolean;
  };
};

const PurchaseDetailsModal = ({
  purchaseSpecialEvent,
  purchaseNft,
  allowanceBalance,
  approveUsdt,
  approveZeroUsdt,
  minePrice,
  unitPriceFromContract,
  loadingType,
}: PurchaseDetailsModalProps) => {
  const NUMBER_OF_PRICE_TAGS = 4;
  const purchaseUnits = [...Array(NUMBER_OF_PRICE_TAGS)].map((_, index) => ({
    value: minePrice && minePrice * BigInt(index + 1),
    label: `${minePrice && minePrice * BigInt(index + 1)}`,
  }));
  const isProcessingPayment =
    loadingType.isApproving ||
    loadingType.isWaitingForApprove ||
    loadingType.isWaitingForConfirmation ||
    loadingType.isMintingNft ||
    loadingType.isApprovingZero ||
    loadingType.isWaitingForApproveZero ||
    loadingType.isSpecialEventLoading ||
    loadingType.isWaitingForSpecialEvent;
  const [loadingStatusMessage, setLoadingStatusMessage] = useState("");

  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const currentMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const sliderDefaultValue = 100;
  const [selectedPurchaseUnits, setSelectedPurchaseUnits] = useState({
    value: sliderDefaultValue,
    label: `${sliderDefaultValue}`,
  });
  const [selectedUnits, setSelectedUnits] = useState(sliderDefaultValue);

  const [isOpenFaucetModal, setIsOpenFaucetModal] = useState(false);
  const [currencyUnit, setCurrencyUnit] = useState("");
  const [symbol, setSymbol] = useState("");
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const tooltipTriggerRef = useRef<HTMLButtonElement>(null);
  const finalUnitPrice =
    unitPriceFromContract && unitPriceFromContract > 0n
      ? unitPriceFromContract
      : minePrice ?? 1n;
  const getPriceDecimals = (chainId?: number) => {
    if (chainId === 56 || chainId === 97) return 1e18;
    if (chainId === 133 || chainId === 177) return 1e6;
    return 1e6;
  };

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const [isSoldOut, setIsSoldOut] = useState(false);
  const { data: gasFee } = useFeeData({
    watch: true,
    enabled: !chain?.unsupported && isConnected && address !== undefined,
    formatUnits: "ether",
  });
  const [showBridgeOptions, setShowBridgeOptions] = useState(false);
  const bridgeRef = useRef<HTMLDivElement>(null);

  const priceDecimals = getPriceDecimals(chain?.id);
  const unitPriceNumber = Number(finalUnitPrice) / priceDecimals;
  const [totalPrice, setTotalPrice] = useState(
    sliderDefaultValue * unitPriceNumber,
  );

  const trimWalletAddress = (address: string) =>
    address.slice(0, 11) + "..." + address.slice(-9);
  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const selectedMine = useSnapshot(mineCardStore).selectedMine;
  const isSpecialEvent = selectedMine === "HSK Special Event";

  const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";
  const isHashkeyNetwork = chain?.id === 133 || chain?.id === 177;

  const purchaseMine = () => {
    if (allowanceBalance == 0n) {
      approveUsdt();
    } else if (
      allowanceBalance &&
      allowanceBalance <
        parseEther(purchaseDetailsModalSnapshot.purchasePrice.toString())
    ) {
      approveZeroUsdt();
    } else {
      purchaseNft();
    }
  };

  const handleTradeClick = () => {
    // 关闭 PurchaseDetailsModal
    purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(false);
    // 切换到 Trade 页面
    navbarButtonSnapshot.setSelectedButton("Trade");
  };

  useEffect(() => {
    if (loadingType.isApproving) {
      setLoadingStatusMessage("Approving");
    }
    if (loadingType.isWaitingForApprove) {
      setLoadingStatusMessage("Waiting for approval");
    }
    if (loadingType.isApprovingZero) {
      setLoadingStatusMessage("Approving ZERO");
    }
    if (loadingType.isWaitingForApproveZero) {
      setLoadingStatusMessage("Waiting for approval ZERO");
    }
    if (loadingType.isMintingNft) {
      setLoadingStatusMessage("Minting NFT");
    }
    if (loadingType.isWaitingForConfirmation) {
      setLoadingStatusMessage("Confirming");
    }
    if (loadingType.isSpecialEventLoading) {
      setLoadingStatusMessage("Minting NFT");
    }
    if (loadingType.isWaitingForSpecialEvent) {
      setLoadingStatusMessage("Confirming");
    }
  }, [
    loadingType.isApproving,
    loadingType.isWaitingForApprove,
    loadingType.isWaitingForConfirmation,
    loadingType.isMintingNft,
    loadingType.isApprovingZero,
    loadingType.isWaitingForApproveZero,
    loadingType.isSpecialEventLoading,
    loadingType.isWaitingForSpecialEvent,
  ]);

  useEffect(() => {
    if (chain?.unsupported) {
      purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(false);
    }

    if (!chain?.unsupported && chain?.id) {
      setCurrencyUnit(networkConfigs[chain?.id].currencyUnit);
      setSymbol(networkConfigs[chain?.id].symbol);
      setTotalPrice(sliderDefaultValue * unitPriceNumber);
    }
  }, [chain]);

  useEffect(() => {
    if (!isConnected) {
      purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(false);
    }
  }, [isConnected]);

  useEffect(() => {
    purchaseDetailsModalSnapshot.setPurchasePrice(
      BigInt(Math.floor(selectedUnits) / currentMine.ipoData.shareOffset),
    );
  }, [totalPrice]);

  useEffect(() => {
    setTotalPrice(
      (Math.floor(selectedUnits) / currentMine.ipoData.shareOffset) *
        unitPriceNumber,
    );
  }, [selectedUnits, unitPriceNumber]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        bridgeRef.current &&
        !bridgeRef.current.contains(event.target as Node)
      ) {
        setShowBridgeOptions(false);
      }
    };

    if (showBridgeOptions) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showBridgeOptions]);

  // 点击外部区域关闭 tooltip
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (
        isTooltipOpen &&
        tooltipTriggerRef.current &&
        !tooltipTriggerRef.current.contains(event.target as Node)
      ) {
        // 检查点击的是否是 tooltip 内容区域
        const target = event.target as Element;
        const tooltipContent =
          target.closest('[data-tooltip-content="true"]') ||
          target.closest("[data-radix-tooltip-content]");

        if (!tooltipContent) {
          setIsTooltipOpen(false);
        }
      }
    };

    if (isTooltipOpen) {
      // 使用 setTimeout 确保点击事件在 tooltip 打开后才开始监听
      const timeoutId = setTimeout(() => {
        document.addEventListener("mousedown", handleClickOutside);
        document.addEventListener("touchstart", handleClickOutside);
      }, 100);

      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener("mousedown", handleClickOutside);
        document.removeEventListener("touchstart", handleClickOutside);
      };
    }
  }, [isTooltipOpen]);

  return (
    <>
      <motion.div
        className={styles.container}
        initial="hidden"
        animate="visible"
      >
        <div className={styles.summaryContainer}>
          <div className={styles.titleWrapper}>
            <h1 className={styles.title}> Purchase Details</h1>
            <div
              className={`${styles.closeButton} ${
                loadingType.isApproving ||
                loadingType.isMintingNft ||
                loadingType.isWaitingForApprove ||
                loadingType.isWaitingForConfirmation ||
                loadingType.isApprovingZero ||
                loadingType.isWaitingForApproveZero ||
                loadingType.isSpecialEventLoading ||
                loadingType.isWaitingForSpecialEvent
                  ? styles.disabled
                  : ""
              }`}
              onClick={() => {
                if (
                  !loadingType.isApproving &&
                  !loadingType.isMintingNft &&
                  !loadingType.isWaitingForApprove &&
                  !loadingType.isWaitingForConfirmation &&
                  !loadingType.isApprovingZero &&
                  !loadingType.isWaitingForApproveZero &&
                  !loadingType.isSpecialEventLoading &&
                  !loadingType.isWaitingForSpecialEvent
                ) {
                  purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                    false,
                  );
                }
              }}
            >
              <img src={crossIcon.src} alt="cross icon" />
            </div>
          </div>
          <div className={styles.scrollArea}>
            {isSoldOut ? (
              <div className={styles.soldoutOverlay}>
                <img src={soldoutImage.src} alt="soldout image" width={100} />
              </div>
            ) : null}
            <div className={styles.imgContainer}>
              <div className={styles.imageFrame}>
                <Image
                  src={information.mineImages[0]}
                  alt={information.name}
                  width={500}
                  height={300}
                  style={{ objectFit: "fill" }}
                />
              </div>
              <div className={styles.information}>
                <div className={styles.logoWrapper}>
                  <div>
                    <h1 className={styles.title}>price</h1>
                    <h1 className={styles.subtitle}>
                      ${information.minePrice}
                    </h1>
                  </div>
                </div>
                <div className={styles.logoWrapper}>
                  <div>
                    <h1 className={styles.title}>Highlight</h1>
                    <h1 className={styles.subtitle}>
                      {information.mineStorage}
                    </h1>
                  </div>
                </div>
              </div>
            </div>
            <Divider />
            <h2 className={styles.summaryTitle}>Account</h2>
            <h3 className={styles.connectedAccount}>
              {address && trimWalletAddress(address)}
            </h3>
            <Divider />
            <h2 className={styles.summaryTitle}>Shares</h2>
            {isSpecialEvent ? (
              <></>
            ) : (
              <div className={styles.sliderContainer}>
                <h3>100</h3>
                <Slider
                  defaultValue={[sliderDefaultValue]}
                  max={1000}
                  min={100}
                  step={100}
                  trackStyle={{ backgroundColor: "yellow", height: 5 }}
                  handleStyle={{
                    borderColor: "white",
                    height: 25,
                    width: 25,
                    marginTop: -10,
                    backgroundColor: "#rgba(0, 196, 208, 1)",
                  }}
                  styles={{
                    tracks: {
                      background: `linear-gradient(to right, blue, red)`,
                    },
                    track: {
                      background: "transparent",
                    },
                  }}
                  onChange={(value) => {
                    const sliderValue = Array.isArray(value) ? value[0] : value;
                    setSelectedUnits(sliderValue);
                  }}
                />
                <h3>1000</h3>
              </div>
            )}

            <h1 className={styles.totalPrice}>
              {Number(totalPrice).toFixed(2)} <span>{currencyUnit}</span>
              <Tooltip.Provider delayDuration={0}>
                <Tooltip.Root
                  open={isTooltipOpen}
                  onOpenChange={(open) => {
                    // 只允许通过点击按钮来控制开关，禁用 hover 行为
                    if (!open) {
                      setIsTooltipOpen(false);
                    }
                  }}
                >
                  <Tooltip.Trigger asChild>
                    <button
                      ref={tooltipTriggerRef}
                      className={styles.informationIconButton}
                      type="button"
                      aria-label="Show price breakdown"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setIsTooltipOpen(!isTooltipOpen);
                      }}
                      onMouseEnter={(e) => {
                        // 禁用 hover 行为
                        e.preventDefault();
                      }}
                      onMouseLeave={(e) => {
                        // 禁用 hover 行为
                        e.preventDefault();
                      }}
                    >
                      <img
                        src={informationIcon.src}
                        alt="information icon"
                        className={styles.informationIcon}
                      />
                    </button>
                  </Tooltip.Trigger>
                  <Tooltip.Portal container={document.body}>
                    <Tooltip.Content
                      side="top"
                      sideOffset={8}
                      className={styles.tooltipContent}
                      avoidCollisions={true}
                      collisionPadding={10}
                      data-tooltip-content="true"
                    >
                      <div className={styles.tooltipDetails}>
                        <h3 className={styles.tooltipTitle}>Price Breakdown</h3>
                        <div className={styles.tooltipInfo}>
                          {/* <p>• 1 NFT = {selectedUnits} ordinary shares</p> */}
                          <p>
                            • Price per share:{" "}
                            {(unitPriceNumber / 100).toFixed(3)} {currencyUnit}
                          </p>
                          <p>• Minimum purchase: 100 shares (1 ITEM)</p>
                          <p>
                            • Total this round: 2,499 ITEMs = 249,900 shares
                          </p>
                          <p>• Gas fees not included</p>
                        </div>
                      </div>
                      <Tooltip.Arrow className={styles.tooltipArrow} />
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </Tooltip.Provider>
            </h1>
            <TransactionDetails
              selectedPurchaseUnits={{
                value: BigInt(selectedUnits),
                label: `${selectedUnits}`,
              }}
              setIsSoldOut={setIsSoldOut}
            />
            {isProcessingPayment ? (
              <div className={styles.loadingContainer}>
                <div className={styles.loadingStatus}>
                  <Image
                    src={doubleRing.src}
                    width={32}
                    height={32}
                    alt="loading icon"
                  />
                  <h3>{loadingStatusMessage}</h3>
                </div>
              </div>
            ) : (
              <>
                <div className={styles.controlButton}>
                  <motion.button
                    whileTap={buttonEffect.tap}
                    className={styles.cancelButton}
                    onClick={() =>
                      purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                        false,
                      )
                    }
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    className={styles.confirmButton}
                    whileTap={buttonEffect.tap}
                    onClick={
                      isSoldOut
                        ? handleTradeClick
                        : isSpecialEvent
                        ? purchaseSpecialEvent
                        : purchaseMine
                    }
                    // disabled={false}
                  >
                    {isSoldOut ? "TRADE" : "BUY"}
                  </motion.button>
                </div>
                <h3 className={styles.gasFee}>
                  Estimated Gas:{" "}
                  <span>
                    {(Number(gasFee?.formatted.maxFeePerGas) * 10 ** 5).toFixed(
                      4,
                    )}{" "}
                  </span>
                  <span style={{ color: "white" }}>{symbol}</span>
                </h3>
                <Divider />
                {isTestnet ? (
                  <>
                    <h3
                      className={styles.testToken}
                      onClick={() => setIsOpenFaucetModal(true)}
                    >
                      Need Test USDT?
                    </h3>
                    <h3
                      className={styles.testToken}
                      onClick={() => {
                        chain &&
                          window.open(
                            networkConfigs[chain?.id].faucet,
                            "_blank",
                          );
                      }}
                    >
                      {`Need Test ${symbol}?`}
                    </h3>
                  </>
                ) : null}
                {isHashkeyNetwork ? (
                  <>
                    <div className={styles.bridgeContainer} ref={bridgeRef}>
                      <h3
                        className={styles.testToken}
                        onClick={() => setShowBridgeOptions(!showBridgeOptions)}
                      >
                        Bridge Funds to HashKey Chain
                      </h3>
                      {showBridgeOptions && (
                        <div className={styles.bridgeOptions}>
                          <div
                            className={styles.bridgeOption}
                            onClick={() => {
                              window.open(
                                "https://www.orbiter.finance/?channel=0xc423a08f039ba977acc7c08d19516fd12561c28b",
                                "_blank",
                              );
                              setShowBridgeOptions(false);
                            }}
                          >
                            <span>Orbiter Finance</span>
                            <span className={styles.bridgeUrl}>
                              orbiter.finance
                            </span>
                          </div>
                          <div
                            className={styles.bridgeOption}
                            onClick={() => {
                              window.open("https://bridge.hsk.xyz/", "_blank");
                              setShowBridgeOptions(false);
                            }}
                          >
                            <span>HashKey Bridge</span>
                            <span className={styles.bridgeUrl}>
                              bridge.hsk.xyz
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                ) : null}
              </>
            )}
          </div>
        </div>

        {isOpenFaucetModal ? (
          <FaucetModal setIsOpenFaucetModal={setIsOpenFaucetModal} />
        ) : null}
      </motion.div>
    </>
  );
};

const Divider = () => {
  return <div className={styles.divider} />;
};

export default PurchaseDetailsModal;
