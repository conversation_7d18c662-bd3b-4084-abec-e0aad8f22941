@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.selectWrapper {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  width: 100%;
}

.label {
  font-family: $font-family-poppins;
  font-size: $font-size-xs;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.required {
  color: $color-danger;
}

.select {
  padding: $spacing-sm $spacing-md;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  border-radius: $border-radius-sm;
  font-family: $font-family-poppins;
  font-size: $font-size-sm;
  background: $color-black-transparent-medium;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: $color-primary;
    box-shadow: 0 0 0 2px rgba(0, 196, 208, 0.1);
  }

  &.error {
    border-color: $color-danger;
  }

  option {
    background: $color-black-transparent-dark;
    color: rgba(255, 255, 255, 0.9);
  }
}

.errorMessage {
  font-family: $font-family-poppins;
  font-size: $font-size-xs;
  color: $color-danger;
  margin-top: -4px;
}
