import React, { useState } from "react";
import styles from "./MobilePage.module.scss";
import { AnimatePresence, motion } from "framer-motion";
import { useAccount } from "wagmi";
import Newsletter from "./NewsLetter/NewsLetter";
// import Transaction from "./Transaction/Transaction";
// import Purchase from "./Purchase/Purchase";
import MineList from "./MineList/MineList";
import PlusModal from "./PlusModal/PlusModal";
import BottomNavigation from "./BottomNavigation/BottomNavigation";
import MineNavigation from "./MineCard/MineNavigation";
import RegisterMine from "./RegisterMine/RegisterMine";
import Profile from "./Profile/Profile";
import PointSystem from "./LeaderBoard/PointSystem";
import PrivateSale from "./PrivateSale/PrivateSale";
import MarketDataCarousel from "./MarketDataCarousel/MarketDataCarousel";
import TradeModal from "./TradeModal/TradeModal";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import { mineCardStore } from "@/stores/mineCard";
import { plusModalStore } from "@/stores/plusModal";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import ConnectWalletModal from "./ConnectWalletModal/ConnectWalletModal";
import { downloadModalStore } from "@/stores/downloadModal";
import FileDownloadModal from "../FileDownloadModal/FileDownloadModal";
import { navbarButtonDetails } from "./BottomNavigation/BottomContents/BottomContents";
import BackGroundPlanet from "@/assets/images/mobileBackgound.png";
import Image from "next/image";
import {
  CameraControls,
  Float,
  Loader,
  OrbitControls,
  Scroll,
  ScrollControls,
  Stars,
  useScroll,
  useProgress,
} from "@react-three/drei";
import { Canvas, useFrame } from "@react-three/fiber";
import StarsScene from "../HomePage/Experience/Scenes/StarsScene";
import { Ref, Suspense, useLayoutEffect, useRef } from "react";
import AsteroidScene from "../HomePage/Experience/Scenes/AsteroidScene";
import Lighting from "../HomePage/Experience/Scenes/Lighting";
import AutoRotate from "../HomePage/Experience/Scenes/AutoRotate";
import GalaxyScene from "../HomePage/Experience/Scenes/GalaxyScene";
import SparklesScene from "../HomePage/Experience/Scenes/SparklesScene";
import CloudScene from "../HomePage/Experience/Scenes/CloudScene";
import { universeAnimationStore } from "@/stores/universeAnimation";
import { useInView } from "react-intersection-observer";
import { useRouter } from "next/navigation";
import { useMediaQuery } from "usehooks-ts";
import EarthScene from "../HomePage/Experience/Scenes/EarthScene";
import ElectronsScene from "../HomePage/Experience/Scenes/ElectronsScene";
import { ToastContainer } from "react-toastify";
import {
  Bloom,
  EffectComposer,
  ToneMapping,
} from "@react-three/postprocessing";
import { ToneMappingMode } from "postprocessing";

const MobilePage = ({ delay = 2 }) => {
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const downloadModalSnapshot = useSnapshot(downloadModalStore);

  const [isButtonClicked, setIsButtonClicked] = useState(false);
  const [showMineNavigation, setShowMineNavigation] = useState(false);
  const [showPointSystem, setShowPointSystem] = useState(false);
  const { isConnected } = useAccount();

  const cameraControlsRef = useRef<CameraControls>();
  const universeAnimationSnapshot = useSnapshot(universeAnimationStore);
  // const scrollContainerSnapshot = useSnapshot(scrollContainerStore);
  const [enableScroll, setEnableScroll] = useState(false);
  const { replace } = useRouter();
  const { progress: modelLoadingProgress } = useProgress();

  const INITIAL_SCALE_FACTOR = 1;
  const [numberOfPages, setNumberOfPages] = useState(0);
  const [scaleFactor, setScaleFactor] = useState(INITIAL_SCALE_FACTOR);

  useLayoutEffect(() => {
    if (modelLoadingProgress === 100) {
      setTimeout(() => {
        setEnableScroll(true);
      }, 5000);
    }
  }, [modelLoadingProgress]);

  const handleMineClick = (mine: string) => {
    // setShowMineNavigation(true);
    navbarButtonSnapshot.setSelectedButton("Mines");
    mineCardSnapshot.setSelectedMine(mine); // 使用 Valtio store 更新选中的矿山
  };

  const handleBack = () => {
    // setShowMineNavigation(false);
    navbarButtonSnapshot.setSelectedButton("overview");
    // return <MineList {...newsletterProps} />;
  };

  const newsletterProps = {
    delay: (delay += 2),
    isButtonClicked,
    setIsButtonClicked,
    showMineNavigation,
    setShowMineNavigation,
    handleMineClick,
  };
  const selectedSection = (selection: string) => {
    const defaultSelection = (
      <>
        <MineList {...newsletterProps} />
        {/* <Profile /> */}
      </>
    );

    let componentToRender;

    switch (selection) {
      case navbarButtonDetails[0].title:
        componentToRender = defaultSelection;
        break;
      case navbarButtonDetails[1].title:
        // componentToRender = <PointSystem />;

        componentToRender = (
          // <>
          //   <AnimatePresence>
          //     {connectWalletModalSnapshot.isOpenConnectWalletModal && (
          //       <ConnectWalletModal />
          //     )}
          //   </AnimatePresence>
          //   <PointSystem />;
          // </>
          <>
            <PrivateSale />
          </>
        );

        break;
      case navbarButtonDetails[2].title:
        componentToRender = (
          <>
            <AnimatePresence>
              {connectWalletModalSnapshot.isOpenConnectWalletModal && (
                <ConnectWalletModal />
              )}
            </AnimatePresence>
            <AnimatePresence>
              {plusModalSnapshot.isOpenPlusModal ? (
                <PlusModal />
              ) : (
                <MineNavigation onBack={handleBack} />
              )}
            </AnimatePresence>
          </>
        );
        break;
      case navbarButtonDetails[3].title:
        // componentToRender = <Newsletter {...newsletterProps} />;
        componentToRender = <TradeModal />;
        break;
      case typeof navbarButtonDetails[4].title === "function"
        ? navbarButtonDetails[4].title(isConnected)
        : navbarButtonDetails[4].title:
        componentToRender = showPointSystem ? (
          <div className={styles.pointSystemContainer}>
            <div
              className={styles.closeButton}
              onClick={() => setShowPointSystem(false)}
            >
              ✕
            </div>
            <PointSystem />
          </div>
        ) : isConnected ? (
          <>
            <Profile
              showPointSystem={showPointSystem}
              setShowPointSystem={setShowPointSystem}
            />
          </>
        ) : (
          <>
            <AnimatePresence>
              {connectWalletModalSnapshot.isOpenConnectWalletModal && (
                <ConnectWalletModal />
              )}
            </AnimatePresence>
            <Profile
              showPointSystem={showPointSystem}
              setShowPointSystem={setShowPointSystem}
            />
          </>
        );
        break;
      default:
        componentToRender = defaultSelection;
    }

    if (showMineNavigation) {
      return <MineNavigation onBack={handleBack} />;
    }

    return componentToRender;
  };

  return (
    <>
      <div className={styles.container}>
        <ToastContainer
          style={{ fontSize: "0.875rem" }}
          position="top-left"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          theme="dark"
        />
        <div className={styles.topBar}>
          <MarketDataCarousel />
        </div>
        <Canvas
          camera={{ position: [0, 0, 1000], fov: 75 }}
          gl={{ preserveDrawingBuffer: true }}
          className={styles.canvas}
        >
          <EffectComposer enableNormalPass>
            <>
              <Bloom
                mipmapBlur
                luminanceThreshold={1}
                levels={8}
                intensity={15}
              />
              <ToneMapping mode={ToneMappingMode.ACES_FILMIC} />
            </>
          </EffectComposer>

          <CameraControls
            ref={cameraControlsRef as Ref<CameraControls>}
            touches={{ one: 0, two: 0, three: 0 }}
            mouseButtons={{ wheel: 0, left: 1, right: 1, middle: 1 }}
          />
          <AutoRotate cameraControlsRef={cameraControlsRef} />
          <Lighting />
          <ScrollControls
            pages={numberOfPages}
            damping={0}
            enabled={enableScroll}
          >
            <Suspense fallback={null}>
              {/* <CloudScene /> */}
              {/* <SparklesScene /> */}
              <GalaxyScene />
              <EarthScene />
              <ElectronsScene />
              <AsteroidScene
                cameraControlsRef={cameraControlsRef}
                replace={replace}
                scaleFactor={scaleFactor}
              />
              <StarsScene />
            </Suspense>
          </ScrollControls>
        </Canvas>

        <div className={styles.backgroundPlanet}>
          <Image
            src={BackGroundPlanet.src}
            alt={"snake icon"}
            layout="fill"
            style={{ objectFit: "cover" }}
          />
        </div>

        <div
          className={styles.content}
          data-selection={navbarButtonSnapshot.selectedButton}
        >
          {selectedSection(navbarButtonSnapshot.selectedButton)}
        </div>

        {/* Global Modals */}
        <AnimatePresence>
          {downloadModalSnapshot.isOpen && <FileDownloadModal />}
        </AnimatePresence>
      </div>
      <BottomNavigation />
    </>
  );
};

export default MobilePage;
