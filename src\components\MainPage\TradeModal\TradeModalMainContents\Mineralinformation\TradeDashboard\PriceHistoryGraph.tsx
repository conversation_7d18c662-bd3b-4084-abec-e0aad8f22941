import React, { useMemo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
} from "recharts";
import useLineChartPriceHistory from "./hooks/useLineChartPriceHistory";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { ContentType } from "recharts/types/component/Tooltip";
import { useTokenDecimals } from "@/hooks/useTokenPrice";
import {
  aggregateAllTimeDaily,
  aggregateForRange,
  buildChartDataFromBuckets,
  formatXAxisLabel,
  RangeLabel,
  InputItem,
} from "./hooks/priceHistoryAggregator";

const PriceHistoryGraph = ({ range = "30D" }: { range?: RangeLabel }) => {
  const { priceHistory, isLoadingHistory } = useLineChartPriceHistory();

  // Use the first paymentToken in the data as the main token for decimals calculation
  const mainPaymentToken = priceHistory[0]?.paymentToken;
  const decimals = useTokenDecimals(mainPaymentToken);

  // Aggregate by selected window and convert bigint values using decimals
  const chartData = useMemo(() => {
    let buckets = aggregateForRange(
      priceHistory as unknown as InputItem[],
      range,
    );
    if (buckets.length === 0 && (priceHistory as any[]).length > 0) {
      buckets = aggregateAllTimeDaily(priceHistory as unknown as InputItem[]);
    }
    return buildChartDataFromBuckets(buckets, decimals);
  }, [priceHistory, range, decimals]);

  if (isLoadingHistory) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          width: "100%",
          flexDirection: "column",
          textAlign: "center",
        }}
      >
        <div
          style={{
            color: "#00C4D0",
            fontSize: "1.25rem",
            marginBottom: "0.5rem",
          }}
        >
          Loading...
        </div>
        <div style={{ color: "#00656B", fontSize: "0.875rem" }}>
          Fetching price history data
        </div>
      </div>
    );
  }

  // Check if price history data is empty
  if (chartData.length === 0) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          width: "100%",
          flexDirection: "column",
          textAlign: "center",
        }}
      >
        <div
          style={{
            color: "#00C4D0",
            fontSize: "1.25rem",
            marginBottom: "0.5rem",
          }}
        >
          No Price History Available
        </div>
        <div style={{ color: "#00656B", fontSize: "0.875rem" }}>
          Historical price data will appear here once available
        </div>
      </div>
    );
  }

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
    label,
  }) => {
    if (active && payload && payload.length) {
      const p: any = payload[0].payload;
      return (
        <div
          style={{
            backgroundColor: "rgba(0, 12, 23, 0.85)",
            padding: "12px",
            border: "1px solid #00656B",
            borderRadius: "6px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <div
            style={{ marginBottom: "4px", color: "#00C4D0", fontWeight: 500 }}
          >
            Date: {label}
          </div>
          <div style={{ color: "#ffffff", fontSize: "0.95rem" }}>
            <div>
              Open:{" "}
              {p.open?.toLocaleString(undefined, { maximumFractionDigits: 6 })}
            </div>
            <div>
              High:{" "}
              {p.high?.toLocaleString(undefined, { maximumFractionDigits: 6 })}
            </div>
            <div>
              Low:{" "}
              {p.low?.toLocaleString(undefined, { maximumFractionDigits: 6 })}
            </div>
            <div>
              Close:{" "}
              {p.close?.toLocaleString(undefined, { maximumFractionDigits: 6 })}
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        <CartesianGrid
          strokeDasharray="3 3"
          stroke="#00656B"
          vertical={false}
          horizontal={false}
        />
        <XAxis
          dataKey="date"
          stroke="#00656B"
          tickFormatter={formatXAxisLabel}
        />
        <YAxis dataKey="priceNumber" stroke="#00656B" />
        <Tooltip content={<CustomTooltip />} isAnimationActive={false} />
        <Line
          type="monotone"
          dataKey="priceNumber"
          fill="black"
          stroke="#00C4D0"
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default PriceHistoryGraph;
