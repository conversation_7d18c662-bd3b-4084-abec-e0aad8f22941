@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(10px);
  @include row-center;
  z-index: 9999;
  padding: $spacing-md;
}

.modalFrame {
  position: relative;
  width: 95vw;
  height: 90vh;
  max-width: 500px;
  max-height: 700px;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(20px);
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-md;
  @include col-center;
  padding: $padding-lg;

  .closeButton {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    width: 40px;
    height: 40px;
    @include row-center;
    cursor: pointer;
    border: $border-width-xs solid $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
    transition: all 0.2s ease;

    &:hover {
      background: $color-primary-contrast;
      border-color: $color-primary;
    }

    img {
      width: 20px;
      height: 20px;
    }
  }
}

.contentWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-lg;
  overflow: hidden;
}

.titleWrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-xs;
  text-align: center;
  width: 100%;

  h1 {
    font-size: $font-size-xl;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
  }

  h2 {
    font-size: $font-size-sm;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
  }
}

.scrollArea {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding-right: $spacing-xs;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
  }
}

.mineGrid {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-md;
  width: 100%;
}

.mineCard {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: $border-radius-md;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: $border-width-xs solid $color-primary-transparent-contrast;

  &:hover {
    border-color: $color-primary;
    transform: translateY(-2px);
  }
}

.mineImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

.mineCardContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  @include col-between;
  padding: $padding-md;
  z-index: 2;
}

.mineName {
  font-size: $font-size-md;
  color: $color-primary;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  margin: 0;
}

.mineDetails {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-xs;

  .mineral {
    font-size: $font-size-sm;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
  }

  .balance {
    font-size: $font-size-sm;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
  }
}

.transferButton {
  background: $color-primary;
  color:  $color-black-transparent-dark;
  border: none;
  border-radius: $border-radius-sm;
  padding: $padding-sm $padding-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-end;

  &:hover {
    background: $color-primary-contrast;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.emptyState {
  @include col-center;
  gap: $spacing-md;
  text-align: center;
  padding: $padding-2xl;
  height: 100%;

  h3 {
    font-size: $font-size-lg;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
  }

  p {
    font-size: $font-size-md;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
    line-height: 1.5;
  }
}

// Mobile specific adjustments
@media (max-width: 480px) {
  .modalFrame {
    width: 98vw;
    height: 95vh;
    padding: $padding-md;
  }

  .titleWrapper h1 {
    font-size: $font-size-lg;
  }

  .titleWrapper h2 {
    font-size: $font-size-xs;
  }

  .mineCard {
    height: 100px;
  }

  .mineCardContent {
    padding: $padding-sm;
  }

  .mineName {
    font-size: $font-size-sm;
  }

  .transferButton {
    padding: $padding-xs $padding-sm;
    font-size: $font-size-xs;
  }
}
