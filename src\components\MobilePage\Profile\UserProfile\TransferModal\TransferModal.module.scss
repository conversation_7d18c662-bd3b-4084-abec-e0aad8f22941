@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100%;
$displayHeight: 100%;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  padding: $spacing-md;
}

.modalFrame {
  position: relative;
  width: 95vw;
  height: 90vh;
  max-width: 500px;
  max-height: 700px;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(20px);
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-md;
  display: flex;
  flex-direction: column;
  padding: $padding-lg;
}

.contentWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-lg;
  overflow: hidden;
}

.titleWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: $spacing-lg;

  .titleContent {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    text-align: left;
    flex: 1;

    h1 {
      font-size: $font-size-xl;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      text-transform: uppercase;
      margin: 0;
    }

    h2 {
      font-size: $font-size-sm;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
      margin: 0;
    }
  }

  .closeButton {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: $border-width-xs solid $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: $spacing-md;

    &:hover {
      background: $color-primary-contrast;
      border-color: $color-primary;
    }

    img {
      width: 20px;
      height: 20px;
    }
  }
}

.scrollArea {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding-right: $spacing-xs;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
  }
}

.mineGrid {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-md;
  width: 100%;
}

.mineCard {
  position: relative;
  width: 100%;
  height: 160px;
  border-radius: $border-radius-md;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: $border-width-xs solid $color-primary-transparent-contrast;

  &:hover {
    border-color: $color-primary;
    transform: translateY(-2px);

    .transferButton {
      transform: scale(1.05);
      background: $color-primary-contrast;
    }
  }
}

.mineImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
}

.mineCardContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $padding-md;
  z-index: 2;
}

.mineInfo {
  text-align: center;
  margin-bottom: $spacing-lg;

  .mineName {
    font-size: $font-size-lg;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0 0 $spacing-sm 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .mineDetails {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .mineral {
      font-size: $font-size-sm;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
      margin: 0;
    }

    .balance {
      font-size: $font-size-sm;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
      margin: 0;
    }
  }
}

.transferButton {
  background: $color-primary;
  color: $color-black-transparent-dark;
  border: 2px solid $color-primary;
  border-radius: $border-radius-md;
  padding: $padding-md $padding-lg;
  font-size: $font-size-md;
  font-weight: $font-weight-bold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  min-height: 48px;
  box-shadow: 0 4px 12px rgba(0, 196, 208, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover {
    background: $color-primary-contrast;
    border-color: $color-primary-contrast;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 196, 208, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: $spacing-md;
  text-align: center;
  padding: $padding-2xl;
  height: 100%;

  h3 {
    font-size: $font-size-lg;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
  }

  p {
    font-size: $font-size-md;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
    line-height: 1.5;
  }
}

// Mobile specific adjustments
@media (max-width: 480px) {
  .modalFrame {
    width: 98vw;
    height: 95%;
    padding: $padding-md;
  }

  .titleWrapper {
    .titleContent {
      h1 {
        font-size: $font-size-lg;
      }

      h2 {
        font-size: $font-size-xs;
      }
    }

    .closeButton {
      width: 36px;
      height: 36px;
      margin-left: $spacing-sm;

      img {
        width: 18px;
        height: 18px;
      }
    }
  }

  .mineCard {
    height: 140px;
  }

  .mineCardContent {
    padding: $padding-sm;
  }

  .mineInfo {
    margin-bottom: $spacing-md;

    .mineName {
      font-size: $font-size-md;
      margin-bottom: $spacing-xs;
    }
  }

  .transferButton {
    padding: $padding-sm $padding-md;
    font-size: $font-size-sm;
    min-width: 160px;
    min-height: 44px;
  }
}
