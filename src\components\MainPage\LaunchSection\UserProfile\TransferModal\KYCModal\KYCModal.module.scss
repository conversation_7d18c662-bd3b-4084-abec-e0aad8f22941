@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(10px);
  @include row-center;
  z-index: 10000;
  padding: $spacing-lg;

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
  }
}

.modalFrame {
  position: relative;
  width: 95vw;
  // height: 85vh;
  max-width: 1400px;
  // max-height: 700px;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(20px);
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-md;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.closeButton {
  position: absolute;
  top: $spacing-md;
  right: $spacing-md;
  width: 40px;
  height: 40px;
  @include row-center;
  cursor: pointer;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    background: $color-primary-contrast;
    border-color: $color-primary;
  }

  img {
    width: 20px;
    height: 20px;
  }
}

.contentWrapper {
  padding: $padding-xl;
  padding-top: 60px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: $spacing-xl;

  h1 {
    font-family: $font-family-orbitron;
    font-size: $font-size-2xl;
    font-weight: 600;
    color: $color-primary;
    margin: 0 0 $spacing-sm 0;
    text-transform: uppercase;
  }

  h2 {
    font-family: $font-family-poppins;
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-weight: 400;
  }
}

.form {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: $spacing-xl;
  flex: 1;
  align-items: start;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.readOnlySection,
.formSection,
.uploadSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;

  h3 {
    font-family: $font-family-orbitron;
    font-size: $font-size-lg;
    font-weight: 600;
    color: $color-primary;
    margin: 0 0 $spacing-md 0;
    padding-bottom: $spacing-sm;
    border-bottom: $border-width-xs solid $color-primary-transparent-contrast;
    text-transform: uppercase;
  }
}

.readOnlyGrid {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.readOnlyField {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  label {
    font-family: $font-family-poppins;
    font-size: $font-size-xs;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  span {
    font-family: $font-family-poppins;
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.9);
    padding: $spacing-sm $spacing-md;
    background: $color-black-transparent-medium;
    border-radius: $border-radius-sm;
    border: $border-width-xs solid $color-primary-transparent-contrast;
  }
}

.formGrid {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.actions {
  grid-column: 1 / -1;
  display: flex;
  gap: $spacing-md;
  justify-content: center;
  padding-top: $spacing-lg;
  border-top: $border-width-xs solid $color-primary-transparent-contrast;
  margin-top: auto;

  @media (max-width: 480px) {
    flex-direction: column;
  }
}

.cancelButton,
.submitButton {
  padding: $spacing-md $spacing-xl;
  border-radius: $border-radius-sm;
  font-family: $font-family-orbitron;
  font-size: $font-size-sm;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  min-width: 140px;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.cancelButton {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);

  &:hover:not(:disabled) {
    background: $color-black-transparent-medium;
    border-color: $color-primary;
    color: $color-primary;
  }
}

.submitButton {
  background: $color-primary;
  color: $color-black-transparent-dark;

  &:hover:not(:disabled) {
    background: $color-primary-contrast;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 196, 208, 0.3);
  }
}
