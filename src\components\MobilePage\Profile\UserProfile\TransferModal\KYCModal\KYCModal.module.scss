@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100%;
$displayHeight: 100%;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(10px);
  @include row-center;
  z-index: 10000;
  padding: $spacing-md;
}

.modalFrame {
  position: relative;
  width: 90%;
  max-width: 500px;
  height: 90%;
  max-height: 700px;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(20px);
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-md;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

// closeButton 样式已移动到 header 内部

.contentWrapper {
  flex: 1;
  padding: $padding-xl $padding-lg $padding-lg;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  // &::-webkit-scrollbar {
  //   width: 4px;
  // }

  // &::-webkit-scrollbar-track {
  //   background: transparent;
  // }

  // &::-webkit-scrollbar-thumb {
  //   background: $color-primary-transparent-contrast;
  //   border-radius: $border-radius-sm;
  // }
  &::-webkit-scrollbar {
    display: none;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: $spacing-lg;

  .headerContent {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    text-align: left;
    flex: 1;

    h1 {
      font-size: $font-size-xl;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      text-transform: uppercase;
      margin: 0;
    }

    h2 {
      font-size: $font-size-sm;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
      margin: 0;
    }
  }

  .closeButton {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: $border-width-xs solid $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: $spacing-md;

    &:hover {
      background: $color-primary-contrast;
      border-color: $color-primary;
    }

    img {
      width: 20px;
      height: 20px;
    }
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  width: 100%;
  flex: 1;
}

.readOnlySection,
.formSection,
.uploadSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  h3 {
    font-size: $font-size-sm;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
    margin-bottom: $spacing-xs;
  }
}

.readOnlyGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-xs;
  width: 100%;
}

.readOnlyField {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: $padding-xs;
  background: rgba(255, 255, 255, 0.05);
  border-radius: $border-radius-sm;
  border: $border-width-xs solid $color-primary-transparent-contrast;

  label {
    font-size: 10px;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    text-transform: uppercase;
    margin: 0;
  }

  span {
    font-size: 11px;
    color: $color-primary;
    font-weight: $font-weight-medium;
    margin: 0;
    word-break: break-all;
    line-height: 1.2;
  }
}

.formGrid {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  width: 100%;
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-sm;
  width: 100%;
  margin-top: auto;
  padding-top: $spacing-md;
  flex-shrink: 0;
}

.cancelButton,
.submitButton {
  flex: 1;
  padding: $padding-sm $padding-md;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;
  border: $border-width-xs solid;
  min-height: 40px;

  &:active {
    transform: scale(0.98);
  }
}

.cancelButton {
  background: transparent;
  color: $color-primary-transparent-contrast;
  border-color: $color-primary-transparent-contrast;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: $color-primary;
    color: $color-primary;
  }
}

.submitButton {
  background: $color-primary;
  color: $color-black-transparent-dark;
  border-color: $color-primary;
  margin-bottom: 1rem;

  &:hover {
    background: $color-primary-contrast;
    border-color: $color-primary-contrast;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover {
      background: $color-primary;
      border-color: $color-primary;
    }
  }
}

// Mobile specific adjustments
@media (max-width: 480px) {
  .modalFrame {
    width: 98vw;
    height: 95%;
  }

  .contentWrapper {
    padding: $padding-md $padding-sm;
  }

  .header {
    margin-bottom: $spacing-md;

    .headerContent {
      h1 {
        font-size: $font-size-md;
      }

      h2 {
        font-size: $font-size-xs;
      }
    }

    .closeButton {
      width: 36px;
      height: 36px;
      margin-left: $spacing-sm;

      img {
        width: 18px;
        height: 18px;
      }
    }
  }

  .readOnlyGrid {
    grid-template-columns: 1fr;
    gap: $spacing-xs;
  }

  .readOnlyField {
    padding: $padding-xs;

    label {
      font-size: 9px;
    }

    span {
      font-size: 10px;
    }
  }

  .formGrid {
    gap: $spacing-xs;
  }

  .actions {
    flex-direction: column;
    gap: $spacing-xs;
    padding-top: $spacing-sm;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
    flex: none;
    min-height: 36px;
    font-size: $font-size-xs;
  }
}
