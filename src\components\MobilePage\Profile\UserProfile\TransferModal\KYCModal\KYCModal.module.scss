@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(10px);
  @include row-center;
  z-index: 10000;
  padding: $spacing-md;
}

.modalFrame {
  position: relative;
  width: 95vw;
  max-width: 500px;
  max-height: 90vh;
  background: $color-black-transparent-dark;
  backdrop-filter: blur(20px);
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-md;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
  }
}

.closeButton {
  position: absolute;
  top: $spacing-md;
  right: $spacing-md;
  width: 40px;
  height: 40px;
  @include row-center;
  cursor: pointer;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    background: $color-primary-contrast;
    border-color: $color-primary;
  }

  img {
    width: 20px;
    height: 20px;
  }
}

.contentWrapper {
  padding: $padding-xl $padding-lg $padding-lg;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-lg;
}

.header {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-sm;
  text-align: center;
  width: 100%;

  h1 {
    font-size: $font-size-xl;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
  }

  h2 {
    font-size: $font-size-sm;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    margin: 0;
    line-height: 1.4;
  }
}

.form {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-lg;
  width: 100%;
}

.readOnlySection,
.formSection,
.uploadSection {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-md;

  h3 {
    font-size: $font-size-md;
    color: $color-primary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
    margin: 0;
  }
}

.readOnlyGrid {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-sm;
  width: 100%;
}

.readOnlyField {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-xs;
  padding: $padding-sm;
  background: rgba(255, 255, 255, 0.05);
  border-radius: $border-radius-sm;
  border: $border-width-xs solid $color-primary-transparent-contrast;

  label {
    font-size: $font-size-xs;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    text-transform: uppercase;
    margin: 0;
  }

  span {
    font-size: $font-size-sm;
    color: $color-primary;
    font-weight: $font-weight-medium;
    margin: 0;
    word-break: break-all;
  }
}

.formGrid {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: $spacing-md;
  width: 100%;
}

.actions {
  @include row-center;
  gap: $spacing-md;
  width: 100%;
  margin-top: $spacing-lg;
}

.cancelButton,
.submitButton {
  flex: 1;
  padding: $padding-md;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;
  border: $border-width-xs solid;
  min-height: 44px; // Better touch target for mobile

  &:active {
    transform: scale(0.98);
  }
}

.cancelButton {
  background: transparent;
  color: $color-primary-transparent-contrast;
  border-color: $color-primary-transparent-contrast;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: $color-primary;
    color: $color-primary;
  }
}

.submitButton {
  background: $color-primary;
  color: $color-black-transparent-dark;
  border-color: $color-primary;
  margin-bottom: 2rem;
  
  &:hover {
    background: $color-primary-contrast;
    border-color: $color-primary-contrast;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover {
      background: $color-primary;
      border-color: $color-primary;
    }
  }
}

// Mobile specific adjustments
@media (max-width: 480px) {
  .modalFrame {
    width: 98vw;
    max-height: 95vh;
  }

  .contentWrapper {
    padding: $padding-lg $padding-md $padding-md;
  }

  .header h1 {
    font-size: $font-size-lg;
  }

  .header h2 {
    font-size: $font-size-xs;
  }

  .readOnlyGrid {
    gap: $spacing-xs;
  }

  .readOnlyField {
    padding: $padding-xs;
  }

  .formGrid {
    gap: $spacing-sm;
  }

  .actions {
    flex-direction: column;
    gap: $spacing-sm;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
    flex: none;
  }
}

// Very small screens
@media (max-width: 360px) {
  .modalFrame {
    width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .contentWrapper {
    padding: $padding-md $padding-sm $padding-sm;
  }
}
