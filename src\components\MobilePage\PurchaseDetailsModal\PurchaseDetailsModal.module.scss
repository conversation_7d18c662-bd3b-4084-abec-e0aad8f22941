@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100%;
$displayHeight: 100%;

@mixin actionButton() {
  width: 100px;
  padding: $padding-sm;
  border: $border-width-2xs solid $color-primary;
  font-size: $font-size-md;
  font-family: $font-family-saira;
  background: $color-black-transparent;
  color: $color-primary-contrast;
  cursor: pointer;

  &:hover {
    background: $color-primary-contrast;
    color: $color-primary;
  }
}

.divider {
  // width: 100%;
  border: $border-width-2xs solid grey;
  margin: $margin-md $margin-md 0;
}

.container {
  position: absolute;
  // border: 1px solid green;
  background: $color-black-transparent-dark;
  @include row-center;
  width: 100%;
  height: 100%;
  // border: $border-width-2xs solid $color-warning;
  // padding: $padding-md 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: $z-index-1;
  // background: green;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    background: linear-gradient(
      to bottom,
      $color-primary,
      $color-black-transparent
    );
    // box-shadow: 0 0 10rem 10rem $color-primary;
    box-shadow:
      0 0 10px $color-primary,
      0 0 20px $color-primary-transparent;
    clip-path: polygon(
      0 0,
      calc(100% - 2rem) 0,
      100% 2rem,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      0.3rem calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 0.2rem) calc(2rem + 0.83px),
      calc(100% - 2rem - 0.83px) 1px,
      1px 1px
    );
  }
}
.summaryContainer {
  // position: absolute;
  width: 100%;
  height: 100%;
  // padding: $padding-md 0;
  // border: $border-width-2xs solid $color-primary;
  background: $color-black-transparent-medium;
  // box-shadow: 0 0 20px $color-primary;

  .titleWrapper {
    // line-height: 108%;
    padding: $padding-md;
    // border-bottom: $border-width-xs solid $color-primary-transparent;
    @include row-between;
    width: 100%;
    // border: 1px solid yellow;

    h1 {
      color: $color-primary;
      font-size: $font-size-md;
      text-transform: uppercase;
      font-weight: $font-weight-semibold;
    }
  }

  .scrollArea {
    position: absolute;
    overflow-y: scroll;
    // border: $border-width-2xs solid $color-primary;
    width: 100%;
    height: 80%;

    .soldoutOverlay {
      position: absolute;
      // background: $color-black-transparent-medium;
      top: 0;
      left: 30;
      right: 0;
      // width: 100%;
      z-index: 99;
      @include col-center;

      // img {
      //   // rotate: -30deg;
      // }
    }

    .sliderContainer {
      @include row-between;
      gap: 1rem;
      padding: 0.5rem $padding-md 0;

      h3 {
        color: $color-warning;
      }

      // .SliderRoot {
      //   position: relative;
      //   display: flex;
      //   align-items: center;
      //   user-select: none;
      //   touch-action: none;
      //   width: 200px;
      //   height: 10px;
      // }

      // .SliderTrack {
      //   background-color: $color-primary-contrast;
      //   position: relative;
      //   flex-grow: 1;
      //   border-radius: 9999px;
      //   height: 3px;
      // }

      // .SliderRange {
      //   position: absolute;
      //   background-color: $color-warning;
      //   border-radius: 9999px;
      //   height: 100%;
      // }

      // .SliderThumb {
      //   display: block;
      //   width: 20px;
      //   height: 20px;
      //   background-color: white;
      //   box-shadow: 0 2px 10px $color-primary;
      //   border-radius: 10px;

      //   &:hover {
      //     background-color: $color-primary;
      //   }
      //   &:focus {
      //     outline: none;
      //     box-shadow: 0 0 0 5px black;
      //   }
      // }
    }

    .imgContainer {
      width: 100%;
      // height: 100%;
      @include row-center;
      // justify-content: flex-start;
      padding: $padding-sm;
      gap: $spacing-sm;

      .imageFrame {
        width: 100%;
        height: 100%;
        position: relative;
        background: $color-primary-transparent;
        backdrop-filter: blur(20px);
        // padding: px;
        clip-path: polygon(
          0 0,
          calc(100% - 32px) 0,
          100% 32px,
          100% 100%,
          0 100%
        );
        // border: 1px solid yellow;

        &::before {
          content: "";
          position: absolute;
          inset: 0;
          background: $color-primary;
          clip-path: polygon(
            0 0,
            calc(100% - 32px) 0,
            100% 32px,
            100% 100%,
            0 100%,
            0 0,
            1px 1px,
            1px calc(100% - 1px),
            calc(100% - 1px) calc(100% - 1px),
            calc(100% - 1px) calc(32px + 0.41px),
            calc(100% - 32px - 0.41px) 1px,
            1px 1px
          );
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .information {
      width: 100%;
      // border-bottom: $border-width-2xs solid $color-primary-transparent;
      padding-bottom: $spacing-sm;

      .logoWrapper {
        // width: 100%;
        @include row-between;

        .title {
          font-size: $font-size-xs;
          color: $color-primary-transparent-contrast;
          font-weight: $font-weight-light;
          text-transform: uppercase;
        }

        .subtitle {
          font-size: $font-size-xs;
          color: $color-primary;
          font-weight: $font-weight-semibold;
        }
      }
    }
    // .closeButton {
    //   position: absolute;
    //   outline: none;
    //   border: none;
    //   border-radius: 100%;
    //   height: 25px;
    //   width: 25px;
    //   display: inline-flex;
    //   align-items: center;
    //   justify-content: center;
    //   background: inherit;
    //   position: absolute;
    //   // top: 28px;
    //   right: $padding-sm;
    //   cursor: pointer;

    //   &:hover {
    //     background-color: $color-primary-transparent-contrast;
    //   }
    // }

    .summaryTitle {
      padding: 0 $padding-md;
      // text-align: center;
    }

    .connectedAccount {
      padding: 0 $padding-md;
      color: $color-warning;
    }

    .sharesWrapper {
      width: 100%;
      @include row-center;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: $spacing-md;
      padding: $padding-sm $padding-md;

      button {
        width: 50px;
        outline: none;
        border: $border-width-2xs solid $color-primary;
        // border-radius: 1rem;
        background: $color-primary-transparent;
        color: $color-primary;
        cursor: pointer;
        padding: $padding-xs;

        // &:hover {
        //   background: $color-primary;
        //   color: $color-primary-contrast;
        // }
      }
    }

    .totalPrice {
      text-align: center;
      color: $color-primary;
      font-size: $font-size-2xl;

      span {
        font-size: $font-size-2xl;
      }

      .informationIconButton {
        background: none;
        border: none;
        padding: 0;
        margin-left: $spacing-sm;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.8;
        }

        &:focus {
          outline: 2px solid $color-primary;
          outline-offset: 2px;
          border-radius: 4px;
        }
      }

      .informationIcon {
        width: 20px;
        height: 20px;
        pointer-events: none;
      }
    }
    .gasFee {
      padding: 0 $padding-md;
      text-align: center;
      margin-top: 0.8rem;

      span {
        color: $color-warning;
      }
    }
    .controlButton {
      @include row-center;
      gap: $spacing-md;
      padding: $padding-sm $padding-md;

      .cancelButton {
        @include actionButton();
        color: $color-danger;
        border: $border-width-2xs solid $color-danger;

        &:hover {
          background: $color-danger;
        }
      }
      .confirmButton {
        @include actionButton();
      }
    }

    .loadingContainer {
      @include row-center;
      padding: $padding-sm $padding-md;

      .loadingStatus {
        @include row-center;
        gap: $spacing-sm;

        h3 {
          color: $color-warning;
          font-size: $font-size-md;
        }
      }
    }

    .testToken {
      text-align: center;
      color: $color-warning;
      text-decoration: underline;
      padding-top: $padding-sm;
      cursor: pointer;
    }

    .bridgeContainer {
      position: relative;

      .bridgeOptions {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 280px;
        background: $color-black-transparent-dark;
        border: $border-width-2xs solid $color-primary;
        border-radius: 8px;
        box-shadow: 0 0 20px rgba(0, 196, 208, 0.3);
        backdrop-filter: blur(30px);
        z-index: 10;
        margin-bottom: 0.25rem;
        padding: $padding-sm;

        .bridgeOption {
          display: flex;
          flex-direction: column;
          padding: $padding-md;
          cursor: pointer;
          border-radius: 6px;
          transition: background-color 0.2s ease;

          &:hover {
            background: $color-primary-transparent;
          }

          &:not(:last-child) {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          }

          span:first-child {
            color: $color-primary-contrast;
            font-weight: $font-weight-medium;
            font-size: $font-size-md;
          }

          .bridgeUrl {
            color: $color-warning;
            font-size: $font-size-sm;
            margin-top: 0.25rem;
          }
        }
      }
    }
  }
}

.tooltipContent {
  z-index: 9999;
  user-select: none;
  animation-name: topDownAndFade;
  animation-duration: 0.3s;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;

  .tooltipDetails {
    min-width: 280px;
    max-width: 320px;
    background: $color-black-transparent-dark;
    backdrop-filter: blur(30px);
    border: $border-width-2xs solid $color-primary;
    padding: $padding-md;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 196, 208, 0.3);

    .tooltipTitle {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      margin-bottom: $margin-sm;
      text-align: center;
    }

    .tooltipInfo {
      p {
        font-size: $font-size-sm;
        color: $color-primary-contrast;
        margin: $margin-sm 0;
        line-height: 1.4;

        &:first-child {
          color: $color-warning;
          font-weight: $font-weight-medium;
        }
      }
    }
  }

  .tooltipArrow {
    fill: $color-primary;
  }
}

@keyframes topDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
