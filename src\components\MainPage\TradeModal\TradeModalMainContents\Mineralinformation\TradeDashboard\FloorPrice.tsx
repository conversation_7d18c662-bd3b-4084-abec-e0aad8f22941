import useLineChartPriceHistory from "./hooks/useLineChartPriceHistory";
import styles from "./FloorPrice.module.scss";
import { formatUnits } from "ethers";
import useUserBalance from "@/components/MainPage/LaunchSection/UserProfile/hooks/useUserBalance";

const FloorPrice = () => {
  const { floorPrice, isLoadingFloor } = useLineChartPriceHistory();
  const { usdtDecimals } = useUserBalance();

  return (
    <div className={styles.floorPriceContainer}>
      <div className={styles.floorPriceHeader}>
        <h3>{floorPrice.isAllTime ? "Floor Price" : "24H Floor Price"}</h3>
      </div>
      {isLoadingFloor ? (
        <div className={styles.floorPriceLoader}>
          <div className={styles.loader}></div>
        </div>
      ) : (
        <div className={styles.floorPriceInfo}>
          <div className={styles.currentFloorPrice}>
            <span className={styles.floorPriceAmount}>
              {formatUnits(BigInt(floorPrice.current), usdtDecimals)} USDT
            </span>
          </div>
          <div className={styles.floorPriceChange}>
            <span
              className={`${styles.changeAmount} ${
                parseFloat(floorPrice.change24h) >= 0
                  ? styles.positive
                  : styles.negative
              }`}
            >
              {parseFloat(floorPrice.change24h) >= 0 ? "+" : ""}
              {formatUnits(BigInt(floorPrice.change24h), usdtDecimals)} USDT
            </span>
            <span
              className={`${styles.changePercentage} ${
                parseFloat(floorPrice.percentage24h) >= 0
                  ? styles.positive
                  : styles.negative
              }`}
            >
              ({parseFloat(floorPrice.percentage24h) >= 0 ? "+" : ""}
              {floorPrice.percentage24h}%)
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloorPrice;
