import { FC, useState } from "react";
import { useForm } from "react-hook-form";
import { MinePurchased } from "@/components/MainPage/LaunchSection/PurchaseHistory/hooks/usePurchaseHistory";
import FormInput from "@/components/common/FormComponents/FormInput";
import FormSelect from "@/components/common/FormComponents/FormSelect";
import FileUpload from "@/components/common/FormComponents/FileUpload";
import styles from "./KYCModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import useUserBalance from "@/components/MainPage/LaunchSection/UserProfile/hooks/useUserBalance";
interface KYCFormData {
  name: string;
  email: string;
  transferNftNumber: number;
  idType: string;
  idFile: File;
}

interface KYCModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedMine: MinePurchased | null;
  walletAddress: string;
  chainName: string;
  onSubmit: (data: KYCFormData) => void;
}

const ID_TYPE_OPTIONS = [
  { value: "passport", label: "Passport" },
  { value: "drivers_license", label: "Driver's License" },
  { value: "national_id", label: "National ID" },
  { value: "other", label: "Other Government ID" },
];

const KYCModal: FC<KYCModalProps> = ({
  isOpen,
  onClose,
  selectedMine,
  walletAddress,
  chainName,
  onSubmit,
}) => {
  const [fileError, setFileError] = useState<string>("");
  const { avaliableNfts: maxNfts } = useUserBalance();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
    trigger,
  } = useForm<KYCFormData>({
    defaultValues: {
      transferNftNumber: 25,
    },
  });

  if (!isOpen || !selectedMine) return null;

  // const maxNfts = parseInt(
  //   selectedMine.tokenDetails.currentUserBalance.toString(),
  // );
  const idFile = watch("idFile");

  const validateFile = (file: File | undefined): string | true => {
    if (!file) return "ID document is required";
    if (file.size > 10 * 1024 * 1024) {
      return "File size must be less than 10MB";
    }
    return true;
  };

  const onFormSubmit = async (data: KYCFormData) => {
    // Validate file upload manually
    const fileValidation = validateFile(idFile);
    if (fileValidation !== true) {
      setFileError(fileValidation);
      return;
    }

    setFileError("");
    onSubmit(data);
  };

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalFrame}>
        <div className={styles.contentWrapper}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <h1>KYC Verification</h1>
              <h2>
                Complete your identity verification to transfer NFTs to shares
              </h2>
            </div>
            <div className={styles.closeButton} onClick={onClose}>
              <img src={crossIcon.src} alt="close" />
            </div>
          </div>

          <form onSubmit={handleSubmit(onFormSubmit)} className={styles.form}>
            <div className={styles.readOnlySection}>
              <h3>Transfer Details</h3>
              <div className={styles.readOnlyGrid}>
                <div className={styles.readOnlyField}>
                  <label>Mine Name</label>
                  <span>{selectedMine.mineDetails.name}</span>
                </div>
                <div className={styles.readOnlyField}>
                  <label>Wallet Address</label>
                  <span>{walletAddress}</span>
                </div>
                <div className={styles.readOnlyField}>
                  <label>Chain</label>
                  <span>{chainName}</span>
                </div>
                <div className={styles.readOnlyField}>
                  <label>Available NFTs</label>
                  <span>{maxNfts}</span>
                </div>
              </div>
            </div>

            <div className={styles.formSection}>
              <h3>Personal Information</h3>
              <div className={styles.formGrid}>
                <FormInput
                  label="Full Name"
                  placeholder="Enter your full legal name"
                  register={register("name", {
                    required: "Full name is required",
                    minLength: {
                      value: 2,
                      message: "Name must be at least 2 characters",
                    },
                  })}
                  error={errors.name?.message}
                  required
                />

                <FormInput
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email address"
                  register={register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: "Invalid email address",
                    },
                  })}
                  error={errors.email?.message}
                  required
                />
              </div>
            </div>

            <div className={styles.formSection}>
              <h3>Transfer Information</h3>
              <div className={styles.formGrid}>
                <FormInput
                  label="Number of NFTs to Transfer"
                  type="number"
                  min={25}
                  max={maxNfts}
                  register={register("transferNftNumber", {
                    required: "Number of NFTs is required",
                    min: {
                      value: 25,
                      message: "Must transfer at least 25 NFT",
                    },
                    max: {
                      value: maxNfts,
                      message: `Cannot transfer more than ${maxNfts} NFTs`,
                    },
                    valueAsNumber: true,
                  })}
                  error={errors.transferNftNumber?.message}
                  required
                />
                <FormSelect
                  label="ID Type"
                  placeholder="Select ID type"
                  options={ID_TYPE_OPTIONS}
                  register={register("idType", {
                    required: "ID type is required",
                  })}
                  error={errors.idType?.message}
                  required
                />
              </div>
            </div>

            <div className={styles.uploadSection}>
              <h3>Identity Document</h3>
              <FileUpload
                label="Upload ID Document"
                fieldName="idFile"
                setValue={(name, value) => {
                  setValue(name as keyof KYCFormData, value);
                  setFileError("");
                }}
                trigger={trigger}
                error={fileError}
                accept="image/*,.pdf"
                required
              />
            </div>

            <div className={styles.actions}>
              <button
                type="button"
                className={styles.cancelButton}
                onClick={onClose}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={styles.submitButton}
                // disabled
                disabled={isSubmitting}
              >
                COMMING SOON
                {isSubmitting ? "Submitting..." : "Submit KYC"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default KYCModal;
