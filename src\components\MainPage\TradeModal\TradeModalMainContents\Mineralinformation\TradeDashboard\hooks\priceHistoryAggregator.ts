// Utilities for aggregating price history into OHLC buckets and building chart data

export type RangeLabel = "24H" | "7D" | "30D";

const MS_IN_HOUR = 60 * 60 * 1000;
const MS_IN_DAY = 24 * MS_IN_HOUR;

export type InputItem = {
  timestamp: number; // seconds
  price: string; // bigint string in smallest unit
  date: string;
  paymentToken: `0x${string}`;
};

export type BucketBigInt = {
  open: bigint;
  high: bigint;
  low: bigint;
  close: bigint;
  dateLabel: string;
  paymentToken: `0x${string}`;
};

function startOfHour(date: Date) {
  const d = new Date(date);
  d.setMinutes(0, 0, 0, 0);
  return d;
}

function startOfDay(date: Date) {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  return d;
}

export function aggregateForRange(
  items: InputItem[],
  range: RangeLabel,
): BucketBigInt[] {
  const now = Date.now();
  const windowMs =
    range === "24H"
      ? MS_IN_DAY
      : range === "7D"
      ? 7 * MS_IN_DAY
      : 30 * MS_IN_DAY;
  const bucketByHour = range === "24H";

  const startTime = now - windowMs;
  const filtered = items.filter((p) => p.timestamp * 1000 >= startTime);

  const map = new Map<string, BucketBigInt>();
  for (const rec of filtered) {
    const dt = new Date(rec.timestamp * 1000);
    const bucketStart = bucketByHour ? startOfHour(dt) : startOfDay(dt);
    const key = bucketStart.toISOString();
    const price = BigInt(rec.price);
    const dateLabel = bucketByHour
      ? `${bucketStart.getMonth() + 1}/${bucketStart.getDate()} ${bucketStart
          .getHours()
          .toString()
          .padStart(2, "0")}:00`
      : `${bucketStart.getFullYear()}-${(bucketStart.getMonth() + 1)
          .toString()
          .padStart(2, "0")}-${bucketStart
          .getDate()
          .toString()
          .padStart(2, "0")}`;

    const existing = map.get(key);
    if (!existing) {
      map.set(key, {
        open: price,
        high: price,
        low: price,
        close: price,
        dateLabel,
        paymentToken: rec.paymentToken,
      });
    } else {
      if (price > existing.high) existing.high = price;
      if (price < existing.low) existing.low = price;
      existing.close = price;
    }
  }

  return Array.from(map.entries())
    .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
    .map(([, v]) => v);
}

export function aggregateAllTimeDaily(items: InputItem[]): BucketBigInt[] {
  const map = new Map<string, BucketBigInt>();
  for (const rec of items) {
    const dt = new Date(rec.timestamp * 1000);
    const bucketStart = startOfDay(dt);
    const key = bucketStart.toISOString();
    const price = BigInt(rec.price);
    const dateLabel = `${bucketStart.getFullYear()}-${(
      bucketStart.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}-${bucketStart.getDate().toString().padStart(2, "0")}`;

    const existing = map.get(key);
    if (!existing) {
      map.set(key, {
        open: price,
        high: price,
        low: price,
        close: price,
        dateLabel,
        paymentToken: rec.paymentToken,
      });
    } else {
      if (price > existing.high) existing.high = price;
      if (price < existing.low) existing.low = price;
      existing.close = price;
    }
  }

  return Array.from(map.entries())
    .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
    .map(([, v]) => v);
}

export type ChartPoint = {
  date: string;
  priceNumber: number; // close scaled for plotting
  open: number;
  high: number;
  low: number;
  close: number;
  openRaw: string; // bigint string
  highRaw: string;
  lowRaw: string;
  closeRaw: string;
  paymentToken: `0x${string}`;
};

export function buildChartDataFromBuckets(
  buckets: BucketBigInt[],
  decimals: number,
): ChartPoint[] {
  const scale = Math.pow(10, decimals);
  return buckets.map((b) => ({
    date: b.dateLabel,
    open: Number(b.open) / scale,
    high: Number(b.high) / scale,
    low: Number(b.low) / scale,
    close: Number(b.close) / scale,
    priceNumber: Number(b.close) / scale,
    openRaw: b.open.toString(),
    highRaw: b.high.toString(),
    lowRaw: b.low.toString(),
    closeRaw: b.close.toString(),
    paymentToken: b.paymentToken,
  }));
}

export function formatXAxisLabel(value: string): string {
  if (!value) return value as unknown as string;
  if (value.includes("-")) {
    const parts = value.split("-");
    return `${parseInt(parts[1], 10)}/${parseInt(parts[2], 10)}`;
  }
  return value;
}
