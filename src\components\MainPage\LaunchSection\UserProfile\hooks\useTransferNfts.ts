import useMineTokenId from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useMineTokenId";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { extractErrorType } from "@/utils/errorHandling";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import {
  useAccount,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import { MinePurchased } from "../../PurchaseHistory/hooks/usePurchaseHistory";

interface UploadData {
  mineName: string;
  walletAddress: `0x${string}`;
  chainName: string;
  erc1155VaultAddress: `0x${string}`;
  txHash: string;
  fullName: string;
  email: string;
  transferNftNumber: number;
  idType: string;
  idFile: string;
}

interface TransferNftsProps {
  setIsKYCModalOpen: (isOpen: boolean) => void;
  setSelectedMine: (mine: MinePurchased | null) => void;
  onClose: () => void;
}

const useTransferNfts = ({
  onClose,
  setIsKYCModalOpen,
  setSelectedMine,
}: TransferNftsProps) => {
  const [uploadData, setUploadData] = useState<UploadData>();
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [erc1155VaultAddress, setErc1155VaultAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [transferNftAmount, setTransferNftAmount] = useState(0);
  const [isApproving, setIsApproving] = useState(false);
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const [errorMessage, setErrorMessage] = useState("");
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setAsteroidAddress(networkConfigs[chain.id].asteroidAddress);
      setErc1155VaultAddress(networkConfigs[chain.id].erc1155VaultAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setAsteroidAddress("0x");
      setErc1155VaultAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain]);

  const { config: transferNftConfig, error: prepareError } =
    usePrepareContractWrite({
      address: asteroidAddress,
      abi: asteroidAddressABI,
      functionName: "safeTransferFrom",
      args: [
        address ?? "0x",
        erc1155VaultAddress,
        BigInt(asteroidMineNumber),
        BigInt(transferNftAmount),
        "0x",
      ],
      enabled:
        isConnected &&
        address !== "0x" &&
        asteroidAddress !== "0x" &&
        erc1155VaultAddress !== "0x" &&
        asteroidMineNumber !== 0 &&
        transferNftAmount !== 0,
      onError: (error: any) => {
        if (error.cause?.reason) {
          setErrorMessage(error.cause.reason);
        } else {
          setErrorMessage(error.shortMessage || error.message);
        }
      },
    });

  const { write: transferNftWrite, data: transferNftData } = useContractWrite({
    ...transferNftConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApproving(false);
    },
  });

  const { isLoading: isWaitingFortransferNft } = useWaitForTransaction({
    confirmations: 5,
    hash: transferNftData?.hash,
    onSuccess: (data) => {
      if (uploadData !== undefined) {
        uploadData.txHash = data.transactionHash;
      }

      // TODO upload KYC data to the server

      showSuccess("Successfully transferred NFTs");
      setIsApproving(false);
      setIsKYCModalOpen(false);
      setSelectedMine(null);
      setTransferNftAmount(0);
      setUploadData(undefined);
      onClose();
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApproving(false);
    },
  });

  const handleTransferNft = () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (errorMessage) {
      showError(errorMessage);
      return;
    }

    if (!transferNftWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      }
      return;
    }

    setIsApproving(true);
    try {
      transferNftWrite?.();
    } catch (error) {
      setIsApproving(false);
      showError(extractErrorType(error) || "Failed to transfer NFTs");
    }
  };

  return {
    setTransferNftAmount,
    isApproving,
    isWaitingFortransferNft,
    handleTransferNft,
    setUploadData,
    erc1155VaultAddress,
  };
};

export default useTransferNfts;
